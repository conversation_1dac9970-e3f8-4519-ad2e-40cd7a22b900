# Glossary

## General terms

### Project

In Unity, you use a project to design and develop a game. A project stores all of the files related to a game, such as the asset and **Scene** files. See [2D or 3D projects](https://docs.unity3d.com/Manual/2Dor3D.html).

### Version Control

A system for managing changes to a set of files. You can use Unity in conjunction with most **version control** tools, including **Unity Version Control** and **Perforce**. See [Version Control](https://docs.unity3d.com/Manual/VersionControl.html).

### Ignore file

A special file used in many **Version Control** Systems which specifies files to be excluded from **version control**. In Unity projects, several folders should be excluded from **version control**.

### Repository

A shared history of changes made to the project's files, saved on a server.

### Workspace

Your local copy of the repository, interacting with the version control system. It's where you download the project's files, make the required changes and perform checkins.

### Check-in

Check-in is the act of submitting changes from your workspace to the shared repository. You can enter a comment before checking in your changes.

## Unity Version Control terms

### Developer Workflow

Developers have access to the branch explorer directly from inside Unity and easily switch branches.

### Gluon Workflow

Artists can take advantage of the Gluon visualized interface and workflow from inside Unity.

### Organization

The organization handles different sets of repositories in the Cloud. Inside the organization, you can create as many repositories as you need.
