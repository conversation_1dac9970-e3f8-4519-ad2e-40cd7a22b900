Shader "Custom/Toon"
{
    Properties
    {
        _MainTex ("Albedo (RGB)", 2D) = "white" {}
        _Color ("Color", Color) = (1,1,1,1)
        _HighlightColor ("Highlight Color", Color) = (1,1,1,1)
        _MidtoneColor ("Midtone Color", Color) = (0.8,0.8,0.8,1)
        _ShadowColor ("Shadow Color", Color) = (0.5,0.5,0.5,1)
        _ShadowThreshold ("Shadow Threshold", Range(0,1)) = 0.05
        _SecondaryLighting ("Secondary Lighting", Range(0,1)) = 1
        _SecondaryLightColor ("Secondary Light Color", Color) = (1,1,1,1)
        _AmbientShading ("Ambient Shading", Range(0,1)) = 0.3
        _Blending ("Blending", Range(0,1)) = 0
        _Smoothness ("Smoothness", Range(0,1)) = 0.5
        _OutlineThreshold ("Outline Threshold", Range(0,1)) = 5
        _CornerBrightness ("Corner Brightness", Color) = (1,1,1,1)
        _Outline ("Outline", Range(0,1)) = 0
    }
    SubShader
    {
        Tags { "RenderType"="Opaque" }
        LOD 200

        CGPROGRAM
        #pragma surface surf Toon

        sampler2D _MainTex;
        fixed4 _Color;
        fixed4 _HighlightColor;
        fixed4 _MidtoneColor;
        fixed4 _ShadowColor;
        half _ShadowThreshold;
        half _SecondaryLighting;
        fixed4 _SecondaryLightColor;
        half _AmbientShading;
        half _Blending;
        half _Smoothness;
        half _OutlineThreshold;
        fixed4 _CornerBrightness;
        half _Outline;

        half4 LightingToon (SurfaceOutput s, half3 lightDir, half atten) {
            half NdotL = dot (s.Normal, lightDir);

            // Create toon lighting zones
            half3 lightColor;
            if (NdotL > 0.8) {
                lightColor = _HighlightColor.rgb;
            } else if (NdotL > _ShadowThreshold) {
                lightColor = lerp(_MidtoneColor.rgb, _HighlightColor.rgb, _Blending);
            } else {
                lightColor = _ShadowColor.rgb;
            }

            // Add secondary lighting and ambient
            lightColor = lerp(lightColor, _SecondaryLightColor.rgb, _SecondaryLighting * 0.1);
            lightColor += _AmbientShading * 0.3;

            half4 c;
            c.rgb = s.Albedo * lightColor * atten;
            c.a = s.Alpha;
            return c;
        }

        struct Input {
            float2 uv_MainTex;
        };

        void surf (Input IN, inout SurfaceOutput o) {
            fixed4 tex = tex2D(_MainTex, IN.uv_MainTex);
            o.Albedo = tex.rgb * _Color.rgb;
            o.Alpha = tex.a * _Color.a;
        }
        ENDCG
    }
    FallBack "Diffuse"
}
