Shader "Custom/Toon"
{
    Properties
    {
        _HighlightColor ("Highlight Color", Color) = (1,1,1,1)
        _MidtoneColor ("Midtone Color", Color) = (0.8,0.8,0.8,1)
        _ShadowColor ("Shadow Color", Color) = (0.5,0.5,0.5,1)
        _ShadowThreshold ("Shadow Threshold", Range(0,1)) = 0.05
        _SecondaryLighting ("Secondary Lighting", Range(0,1)) = 1
        _SecondaryLightColor ("Secondary Light Color", Color) = (1,1,1,1)
        _AmbientShading ("Ambient Shading", Range(0,1)) = 0.3
        _Blending ("Blending", Range(0,1)) = 0
        _Smoothness ("Smoothness", Range(0,1)) = 0.5
        _OutlineThreshold ("Outline Threshold", Range(0,1)) = 5
        _CornerBrightness ("Corner Brightness", Color) = (1,1,1,1)
        _Outline ("Outline", Range(0,1)) = 0
    }
    SubShader
    {
        Tags { "RenderType"="Opaque" }
        LOD 200

        CGPROGRAM
        #pragma surface surf Toon

        fixed4 _HighlightColor;
        fixed4 _MidtoneColor;
        fixed4 _ShadowColor;
        half _ShadowThreshold;
        half _SecondaryLighting;
        fixed4 _SecondaryLightColor;
        half _AmbientShading;
        half _Blending;
        half _Smoothness;
        half _OutlineThreshold;
        fixed4 _CornerBrightness;
        half _Outline;

        half4 LightingToon (SurfaceOutput s, half3 lightDir, half3 viewDir, half atten) {
            half NdotL = dot (s.Normal, lightDir);
            half NdotV = dot (s.Normal, viewDir);

            // Create toon lighting zones with smooth transitions
            half3 baseColor;
            half lightStep = smoothstep(_ShadowThreshold - 0.01, _ShadowThreshold + 0.01, NdotL);
            half highlightStep = smoothstep(0.7, 0.9, NdotL);

            // Blend between shadow, midtone, and highlight
            baseColor = lerp(_ShadowColor.rgb, _MidtoneColor.rgb, lightStep);
            baseColor = lerp(baseColor, _HighlightColor.rgb, highlightStep * _Blending);

            // Add secondary lighting influence
            half3 secondaryInfluence = _SecondaryLightColor.rgb * _SecondaryLighting * 0.2;
            baseColor += secondaryInfluence;

            // Add ambient shading
            baseColor += _AmbientShading * 0.2;

            // Corner/edge brightness (using view angle)
            half edgeFactor = 1.0 - abs(NdotV);
            baseColor += _CornerBrightness.rgb * edgeFactor * _Outline * 0.1;

            // Apply smoothness as a subtle specular-like effect
            half spec = pow(max(0, dot(reflect(-lightDir, s.Normal), viewDir)), 32.0 * _Smoothness);
            baseColor += spec * _HighlightColor.rgb * _Smoothness;

            half4 c;
            c.rgb = s.Albedo * baseColor * atten;
            c.a = s.Alpha;
            return c;
        }

        struct Input {
            float2 uv_dummy;
        };

        void surf (Input IN, inout SurfaceOutput o) {
            // Use midtone color as base albedo since we removed MainTex and Color
            o.Albedo = _MidtoneColor.rgb;
            o.Alpha = _MidtoneColor.a;
        }
        ENDCG
    }
    FallBack "Diffuse"
}
