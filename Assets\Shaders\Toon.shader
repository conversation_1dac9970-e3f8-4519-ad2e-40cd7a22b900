Shader "Custom/Toon"
{
    Properties
    {
        _HighlightColor ("Highlight Color", Color) = (1,1,1,1)
        _MidtoneColor ("Midtone Color", Color) = (0.8,0.8,0.8,1)
        _ShadowColor ("Shadow Color", Color) = (0.5,0.5,0.5,1)
        _ShadowThreshold ("Shadow Threshold", Range(0,1)) = 0.05
        _SecondaryLighting ("Secondary Lighting", Range(0,1)) = 1
        _SecondaryLightColor ("Secondary Light Color", Color) = (1,1,1,1)
        _AmbientShading ("Ambient Shading", Range(0,1)) = 0.3
        _Blending ("Blending", Range(0,1)) = 0
        _Smoothness ("Smoothness", Range(0,1)) = 0.5
        _OutlineThreshold ("Outline Threshold", Range(0,1)) = 5
        _CornerBrightness ("Corner Brightness", Color) = (1,1,1,1)
        _Outline ("Outline", Range(0,1)) = 0

        // Outline properties based on Roystan tutorial
        _OutlineColor ("Outline Color", Color) = (0,0,0,1)
        _OutlineWidth ("Outline Width", Range(0,10)) = 1
    }
    SubShader
    {
        Tags { "RenderType"="Opaque" }
        LOD 200

        // Outline pass (rendered first)
        Pass
        {
            Name "Outline"
            Tags { "LightMode" = "Always" }
            Cull Front
            ZWrite On
            ColorMask RGB
            Blend SrcAlpha OneMinusSrcAlpha

            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #include "UnityCG.cginc"

            struct appdata
            {
                float4 vertex : POSITION;
                float3 normal : NORMAL;
            };

            struct v2f
            {
                float4 pos : SV_POSITION;
                fixed4 color : COLOR;
            };

            fixed4 _OutlineColor;
            float _OutlineWidth;

            v2f vert(appdata v)
            {
                v2f o;

                // Expand vertices along normals for outline
                float3 norm = normalize(mul((float3x3)UNITY_MATRIX_IT_MV, v.normal));
                float2 offset = TransformViewToProjection(norm.xy);

                o.pos = UnityObjectToClipPos(v.vertex);
                o.pos.xy += offset * o.pos.z * _OutlineWidth * 0.01;
                o.color = _OutlineColor;
                return o;
            }

            fixed4 frag(v2f i) : SV_Target
            {
                return i.color;
            }
            ENDCG
        }

        // Main toon shading pass
        CGPROGRAM
        #pragma surface surf Toon

        fixed4 _HighlightColor;
        fixed4 _MidtoneColor;
        fixed4 _ShadowColor;
        half _ShadowThreshold;
        half _SecondaryLighting;
        fixed4 _SecondaryLightColor;
        half _AmbientShading;
        half _Blending;
        half _Smoothness;
        half _OutlineThreshold;
        fixed4 _CornerBrightness;
        half _Outline;
        fixed4 _OutlineColor;
        float _OutlineWidth;

        half4 LightingToon (SurfaceOutput s, half3 lightDir, half3 viewDir, half atten) {
            half NdotL = dot (s.Normal, lightDir);
            half NdotV = dot (s.Normal, viewDir);

            // Create toon lighting zones with smooth transitions
            half3 baseColor;
            half lightStep = smoothstep(_ShadowThreshold - 0.01, _ShadowThreshold + 0.01, NdotL);
            half highlightStep = smoothstep(0.7, 0.9, NdotL);

            // Blend between shadow, midtone, and highlight
            baseColor = lerp(_ShadowColor.rgb, _MidtoneColor.rgb, lightStep);
            baseColor = lerp(baseColor, _HighlightColor.rgb, highlightStep * _Blending);

            // Add secondary lighting influence
            half3 secondaryInfluence = _SecondaryLightColor.rgb * _SecondaryLighting * 0.2;
            baseColor += secondaryInfluence;

            // Add ambient shading
            baseColor += _AmbientShading * 0.2;

            // Corner/edge brightness (using view angle)
            half edgeFactor = 1.0 - abs(NdotV);
            baseColor += _CornerBrightness.rgb * edgeFactor * _Outline * 0.1;

            // Apply smoothness as a subtle specular-like effect
            half spec = pow(max(0, dot(reflect(-lightDir, s.Normal), viewDir)), 32.0 * _Smoothness);
            baseColor += spec * _HighlightColor.rgb * _Smoothness;

            half4 c;
            c.rgb = s.Albedo * baseColor * atten;
            c.a = s.Alpha;
            return c;
        }

        struct Input {
            float2 uv_dummy;
        };

        void surf (Input IN, inout SurfaceOutput o) {
            // Use midtone color as base albedo since we removed MainTex and Color
            o.Albedo = _MidtoneColor.rgb;
            o.Alpha = _MidtoneColor.a;
        }
        ENDCG
    }
    FallBack "Diffuse"
}
