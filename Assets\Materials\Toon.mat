%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Toon
  m_Shader: {fileID: 4800000, guid: 2f457763e2aed6b4bbe0c7712837a9ff, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords: []
  m_InvalidKeywords: []
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 1
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BumpMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _RampTex:
        m_Texture: {fileID: 10914, guid: 0000000000000000f000000000000000, type: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AmbientShading: 1
    - _Blending: 0
    - _BumpScale: 1
    - _Cutoff: 0.5
    - _DetailNormalMapScale: 1
    - _DstBlend: 0
    - _GlossMapScale: 1
    - _Glossiness: 0.5
    - _GlossyReflections: 1
    - _Metallic: 0
    - _Mode: 0
    - _OcclusionStrength: 1
    - _Outline: 0
    - _OutlineThreshold: 1
    - _OutlineWidth: 1
    - _Parallax: 0.02
    - _RimAmount: 0.44
    - _RimThreshold: 0.27
    - _SecondaryLighting: 1
    - _ShadowSmoothness: 0.1
    - _ShadowThreshold: 0.48
    - _Smoothness: 0.5
    - _SmoothnessTextureChannel: 0
    - _SpecularHighlights: 1
    - _SrcBlend: 1
    - _UVSec: 0
    - _ZWrite: 1
    m_Colors:
    - _AmbientColor: {r: 0, g: 0.30755594, b: 0.764151, a: 1}
    - _Color: {r: 0.4777534, g: 0.6981132, b: 0.21404415, a: 1}
    - _CornerBrightness: {r: 1, g: 1, b: 1, a: 1}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _HighlightColor: {r: 0.059032843, g: 0.5188679, b: 0, a: 1}
    - _MidtoneColor: {r: 0.25746778, g: 0.735849, b: 0.20478818, a: 1}
    - _OutlineColor: {r: 1, g: 1, b: 1, a: 1}
    - _RimColor: {r: 1, g: 1, b: 1, a: 1}
    - _SecondaryLightColor: {r: 1, g: 1, b: 1, a: 1}
    - _ShadowColor: {r: 0, g: 0.17539205, b: 0.3773585, a: 1}
    - _SpecularColor: {r: 0.8207547, g: 0.62364143, b: 0.042586334, a: 1}
  m_BuildTextureStacks: []
  m_AllowLocking: 1
