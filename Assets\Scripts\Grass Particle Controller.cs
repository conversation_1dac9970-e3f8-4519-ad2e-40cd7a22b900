using UnityEngine;
using System.Collections.Generic;

public class GrassParticleController : MonoBehaviour
{
    [Header("Grass Settings")]
    public Material grassMaterial;
    public int grassDensity = 1000;
    public float grassHeight = 1f;
    public float grassWidth = 0.5f;
    public Vector2 grassSizeVariation = new Vector2(0.8f, 1.2f);

    [Header("Terrain Settings")]
    public Terrain terrain;
    public LayerMask terrainLayer = 1;

    [Header("Performance")]
    public bool enableBillboard = true;
    public float maxDistance = 50f;

    private Mesh grassMesh;
    private MeshFilter meshFilter;
    private MeshRenderer meshRenderer;
    private Camera mainCamera;
    private Vector3[] originalVertices;
    private Vector3[] billboardVertices;

    void Start()
    {
        mainCamera = Camera.main;
        if (mainCamera == null)
            mainCamera = FindObjectOfType<Camera>();

        SetupMeshComponents();
        GenerateGrassMesh();
    }

    void Update()
    {
        if (enableBillboard && mainCamera != null)
        {
            UpdateBillboard();
        }
    }

    void SetupMeshComponents()
    {
        meshFilter = GetComponent<MeshFilter>();
        if (meshFilter == null)
            meshFilter = gameObject.AddComponent<MeshFilter>();

        meshRenderer = GetComponent<MeshRenderer>();
        if (meshRenderer == null)
            meshRenderer = gameObject.AddComponent<MeshRenderer>();

        if (grassMaterial != null)
            meshRenderer.material = grassMaterial;
    }

    void GenerateGrassMesh()
    {
        if (terrain == null)
        {
            Debug.LogError("No terrain assigned to Grass Particle Controller!");
            return;
        }

        List<Vector3> vertices = new List<Vector3>();
        List<Vector2> uvs = new List<Vector2>();
        List<int> triangles = new List<int>();
        List<Color> colors = new List<Color>();

        TerrainData terrainData = terrain.terrainData;
        Vector3 terrainPosition = terrain.transform.position;
        Vector3 terrainSize = terrainData.size;

        // Calculate grass positions
        for (int i = 0; i < grassDensity; i++)
        {
            // Random position on terrain
            float x = Random.Range(0f, terrainSize.x);
            float z = Random.Range(0f, terrainSize.z);

            // Get height at this position
            float normalizedX = x / terrainSize.x;
            float normalizedZ = z / terrainSize.z;
            float y = terrainData.GetInterpolatedHeight(normalizedX, normalizedZ);

            Vector3 worldPos = terrainPosition + new Vector3(x, y, z);

            // Create quad for this grass blade
            CreateGrassQuad(vertices, uvs, triangles, colors, worldPos, i);
        }

        // Create and assign mesh
        grassMesh = new Mesh();
        grassMesh.name = "Grass Mesh";
        grassMesh.vertices = vertices.ToArray();
        grassMesh.uv = uvs.ToArray();
        grassMesh.triangles = triangles.ToArray();
        grassMesh.colors = colors.ToArray();
        grassMesh.RecalculateNormals();
        grassMesh.RecalculateBounds();

        meshFilter.mesh = grassMesh;

        // Store original vertices for billboard calculations
        originalVertices = vertices.ToArray();
        billboardVertices = new Vector3[originalVertices.Length];
    }

    void CreateGrassQuad(List<Vector3> vertices, List<Vector2> uvs, List<int> triangles, List<Color> colors, Vector3 position, int grassIndex)
    {
        // Random size variation
        float sizeMultiplier = Random.Range(grassSizeVariation.x, grassSizeVariation.y);
        float height = grassHeight * sizeMultiplier;
        float width = grassWidth * sizeMultiplier;

        // Random rotation around Y axis
        float rotation = Random.Range(0f, 360f);
        Quaternion rot = Quaternion.Euler(0, rotation, 0);

        // Create quad vertices (billboard style - facing camera initially)
        Vector3 bottomLeft = position + rot * new Vector3(-width * 0.5f, 0, 0);
        Vector3 bottomRight = position + rot * new Vector3(width * 0.5f, 0, 0);
        Vector3 topLeft = position + rot * new Vector3(-width * 0.5f, height, 0);
        Vector3 topRight = position + rot * new Vector3(width * 0.5f, height, 0);

        // Convert to local space
        bottomLeft = transform.InverseTransformPoint(bottomLeft);
        bottomRight = transform.InverseTransformPoint(bottomRight);
        topLeft = transform.InverseTransformPoint(topLeft);
        topRight = transform.InverseTransformPoint(topRight);

        int vertexIndex = vertices.Count;

        // Add vertices
        vertices.Add(bottomLeft);
        vertices.Add(bottomRight);
        vertices.Add(topLeft);
        vertices.Add(topRight);

        // Add UVs
        uvs.Add(new Vector2(0, 0));
        uvs.Add(new Vector2(1, 0));
        uvs.Add(new Vector2(0, 1));
        uvs.Add(new Vector2(1, 1));

        // Add triangles (two triangles make a quad)
        triangles.Add(vertexIndex);
        triangles.Add(vertexIndex + 2);
        triangles.Add(vertexIndex + 1);

        triangles.Add(vertexIndex + 1);
        triangles.Add(vertexIndex + 2);
        triangles.Add(vertexIndex + 3);

        // Add colors (can be used for wind animation or variation)
        Color grassColor = Color.white;
        grassColor.a = Random.Range(0.8f, 1f); // Slight alpha variation
        colors.Add(grassColor);
        colors.Add(grassColor);
        colors.Add(grassColor);
        colors.Add(grassColor);
    }

    void UpdateBillboard()
    {
        if (originalVertices == null || grassMesh == null)
            return;

        Vector3 cameraPosition = mainCamera.transform.position;
        Vector3 cameraForward = mainCamera.transform.forward;

        // Update vertices to face camera
        for (int i = 0; i < originalVertices.Length; i += 4)
        {
            // Get the base position (bottom center of grass quad)
            Vector3 grassWorldPos = transform.TransformPoint((originalVertices[i] + originalVertices[i + 1]) * 0.5f);

            // Calculate distance to camera for LOD
            float distanceToCamera = Vector3.Distance(grassWorldPos, cameraPosition);

            if (distanceToCamera > maxDistance)
            {
                // Hide distant grass by collapsing vertices
                billboardVertices[i] = originalVertices[i];
                billboardVertices[i + 1] = originalVertices[i];
                billboardVertices[i + 2] = originalVertices[i];
                billboardVertices[i + 3] = originalVertices[i];
                continue;
            }

            // Calculate billboard direction (camera to grass, projected on XZ plane)
            Vector3 directionToCamera = (cameraPosition - grassWorldPos).normalized;
            directionToCamera.y = 0; // Keep grass upright
            directionToCamera.Normalize();

            // Calculate right vector for billboard orientation
            Vector3 right = Vector3.Cross(Vector3.up, directionToCamera);

            // Get original grass dimensions
            Vector3 bottomLeft = originalVertices[i];
            Vector3 bottomRight = originalVertices[i + 1];
            Vector3 topLeft = originalVertices[i + 2];
            Vector3 topRight = originalVertices[i + 3];

            // Calculate grass center and dimensions
            Vector3 center = (bottomLeft + bottomRight + topLeft + topRight) * 0.25f;
            float width = Vector3.Distance(bottomLeft, bottomRight);
            float height = Vector3.Distance(bottomLeft, topLeft);

            // Create new billboard vertices
            Vector3 rightOffset = transform.InverseTransformDirection(right) * width * 0.5f;
            Vector3 upOffset = Vector3.up * height;

            Vector3 basePos = (bottomLeft + bottomRight) * 0.5f;

            billboardVertices[i] = basePos - rightOffset; // bottom left
            billboardVertices[i + 1] = basePos + rightOffset; // bottom right
            billboardVertices[i + 2] = basePos - rightOffset + upOffset; // top left
            billboardVertices[i + 3] = basePos + rightOffset + upOffset; // top right
        }

        // Update mesh
        grassMesh.vertices = billboardVertices;
        grassMesh.RecalculateNormals();
        grassMesh.RecalculateBounds();
    }

    void OnDrawGizmosSelected()
    {
        if (terrain != null)
        {
            Gizmos.color = Color.green;
            TerrainData terrainData = terrain.terrainData;
            Vector3 terrainPos = terrain.transform.position;
            Vector3 terrainSize = terrainData.size;

            Gizmos.DrawWireCube(terrainPos + terrainSize * 0.5f, terrainSize);
        }
    }
}
