Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.34f1 (5ab2d9ed9190) revision 5944025'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.22631) 64bit CoreSingleLanguage' Language: 'en' Physical Memory: 16251 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
E:\Unity\Editior\6000.0.34f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
E:/Unity/Projects/t3essl8r
-logFile
Logs/AssetImportWorker0.log
-srvPort
64116
-job-worker-count
3
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: E:/Unity/Projects/t3essl8r
E:/Unity/Projects/t3essl8r
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [2772]  Target information:

Player connection [2772]  * "[IP] *********** [Port] 0 [Flags] 2 [Guid] 584922332 [EditorId] 584922332 [Version] 1048832 [Id] WindowsEditor(7,Atichat) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [2772] Host joined multi-casting on [***********:54997]...
Player connection [2772] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 3
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 1.92 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.34f1 (5ab2d9ed9190)
[Subsystems] Discovering subsystems at path E:/Unity/Editior/6000.0.34f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path E:/Unity/Projects/t3essl8r/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce GTX 1650 Ti (ID=0x1f95)
    Vendor:   NVIDIA
    VRAM:     3935 MB
    Driver:   32.0.15.7680
Initialize mono
Mono path[0] = 'E:/Unity/Editior/6000.0.34f1/Editor/Data/Managed'
Mono path[1] = 'E:/Unity/Editior/6000.0.34f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'E:/Unity/Editior/6000.0.34f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56676
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: E:/Unity/Editior/6000.0.34f1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: E:/Unity/Editior/6000.0.34f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.003808 seconds.
- Loaded All Assemblies, in  0.657 seconds
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.531 seconds
Domain Reload Profiling: 1187ms
	BeginReloadAssembly (242ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (67ms)
	RebuildNativeTypeToScriptingClass (22ms)
	initialDomainReloadingComplete (70ms)
	LoadAllAssembliesAndSetupDomain (254ms)
		LoadAssemblies (242ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (249ms)
			TypeCache.Refresh (247ms)
				TypeCache.ScanAssembly (225ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (532ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (476ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (30ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (84ms)
			ProcessInitializeOnLoadAttributes (270ms)
			ProcessInitializeOnLoadMethodAttributes (86ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.004 seconds
Refreshing native plugins compatible for Editor in 1.75 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.851 seconds
Domain Reload Profiling: 1855ms
	BeginReloadAssembly (286ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (39ms)
	RebuildCommonClasses (62ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (45ms)
	LoadAllAssembliesAndSetupDomain (594ms)
		LoadAssemblies (455ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (323ms)
			TypeCache.Refresh (258ms)
				TypeCache.ScanAssembly (235ms)
			BuildScriptInfoCaches (55ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (851ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (676ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (121ms)
			ProcessInitializeOnLoadAttributes (508ms)
			ProcessInitializeOnLoadMethodAttributes (37ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.01 seconds
Refreshing native plugins compatible for Editor in 1.80 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 10 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3705 unused Assets / (1.2 MB). Loaded Objects now: 4207.
Memory consumption went from 92.2 MB to 91.0 MB.
Total: 8.149000 ms (FindLiveObjects: 0.536900 ms CreateObjectMapping: 0.652400 ms MarkObjects: 5.056900 ms  DeleteObjects: 1.901600 ms)

========================================================================
Received Import Request.
  Time since last request: 92480.309177 seconds.
  path: Assets/Materials/Toon.mat
  artifactKey: Guid(bb29dd654592552448864d4451f56f65) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Materials/Toon.mat using Guid(bb29dd654592552448864d4451f56f65) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter)Shader error in 'Custom/Toon': undeclared identifier '_MainTex_ST' at Assets/Shaders/Toon.shader(122) (on d3d11)

Compiling Subshader: 0, Pass: <Unnamed Pass 1>, Vertex program with DIRECTIONAL LIGHTPROBE_SH
Platform defines: SHADER_API_DESKTOP UNITY_ENABLE_DETAIL_NORMALMAP UNITY_ENABLE_REFLECTION_BUFFERS UNITY_LIGHTMAP_FULL_HDR UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_PASS_FORWARDBASE UNITY_PBS_USE_BRDF1 UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_SPECCUBE_BLENDING UNITY_SPECCUBE_BOX_PROJECTION UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS
Disabled keywords: DIRLIGHTMAP_COMBINED DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING SHADER_API_GLES30 SHADOWS_SCREEN SHADOWS_SHADOWMASK UNITY_ASTC_NORMALMAP_ENCODING UNITY_COLORSPACE_GAMMA UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_RGBM_ENCODING UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_NO_DXT5nm UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 UNITY_PBS_USE_BRDF3 UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_UNIFIED_SHADER_PRECISION_MODEL UNITY_VIRTUAL_TEXTURING VERTEXLIGHT_ON
Shader error in 'Custom/Toon': undeclared identifier '_MainTex_ST' at Assets/Shaders/Toon.shader(122) (on d3d11)

Compiling Subshader: 0, Pass: <Unnamed Pass 1>, Vertex program with DIRECTIONAL LIGHTPROBE_SH
Platform defines: SHADER_API_DESKTOP UNITY_ENABLE_DETAIL_NORMALMAP UNITY_ENABLE_REFLECTION_BUFFERS UNITY_LIGHTMAP_FULL_HDR UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_PASS_FORWARDBASE UNITY_PBS_USE_BRDF1 UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_SPECCUBE_BLENDING UNITY_SPECCUBE_BOX_PROJECTION UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS
Disabled keywords: DIRLIGHTMAP_COMBINED DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING SHADER_API_GLES30 SHADOWS_SCREEN SHADOWS_SHADOWMASK UNITY_ASTC_NORMALMAP_ENCODING UNITY_COLORSPACE_GAMMA UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_RGBM_ENCODING UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_NO_DXT5nm UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 UNITY_PBS_USE_BRDF3 UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_UNIFIED_SHADER_PRECISION_MODEL UNITY_VIRTUAL_TEXTURING VERTEXLIGHT_ON
 -> (artifact id: '22e78c4f654e2ff029cc28dc915db0f1') in 0.2043441 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 7.034823 seconds.
  path: Assets/Shaders/Toon.shader
  artifactKey: Guid(2f457763e2aed6b4bbe0c7712837a9ff) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Shaders/Toon.shader using Guid(2f457763e2aed6b4bbe0c7712837a9ff) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '32e79bbd07e80bd105a56ce414cec0b4') in 0.001323 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

