{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 22320, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 22320, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 22320, "tid": 555, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 22320, "tid": 555, "ts": 1751899782701228, "dur": 971, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 22320, "tid": 555, "ts": 1751899782707124, "dur": 953, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 22320, "tid": 21474836480, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780382785, "dur": 24638, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780407425, "dur": 2284199, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780407435, "dur": 165, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780407674, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780407677, "dur": 63442, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780471130, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780471135, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780471175, "dur": 7, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780471183, "dur": 2946, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780474137, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780474141, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780474211, "dur": 3, "ph": "X", "name": "ProcessMessages 670", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780474217, "dur": 46, "ph": "X", "name": "ReadAsync 670", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780474267, "dur": 1, "ph": "X", "name": "ProcessMessages 188", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780474270, "dur": 46, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780474320, "dur": 1, "ph": "X", "name": "ProcessMessages 105", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780474322, "dur": 52, "ph": "X", "name": "ReadAsync 105", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780474379, "dur": 2, "ph": "X", "name": "ProcessMessages 357", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780474382, "dur": 44, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780474429, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780474432, "dur": 54, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780474490, "dur": 1, "ph": "X", "name": "ProcessMessages 386", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780474492, "dur": 46, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780474542, "dur": 1, "ph": "X", "name": "ProcessMessages 586", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780474544, "dur": 54, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780474606, "dur": 2, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780474627, "dur": 77, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780474708, "dur": 5, "ph": "X", "name": "ProcessMessages 692", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780474715, "dur": 46, "ph": "X", "name": "ReadAsync 692", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780474765, "dur": 65, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780474833, "dur": 1, "ph": "X", "name": "ProcessMessages 1", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780474835, "dur": 39, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780474879, "dur": 1, "ph": "X", "name": "ProcessMessages 169", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780474881, "dur": 41, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780474928, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780474989, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780474993, "dur": 47, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780475043, "dur": 1, "ph": "X", "name": "ProcessMessages 302", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780475045, "dur": 47, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780475097, "dur": 1, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780475100, "dur": 73, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780475176, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780475178, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780475230, "dur": 2, "ph": "X", "name": "ProcessMessages 723", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780475233, "dur": 54, "ph": "X", "name": "ReadAsync 723", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780475291, "dur": 1, "ph": "X", "name": "ProcessMessages 436", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780475294, "dur": 57, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780475361, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780475364, "dur": 71, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780475438, "dur": 1, "ph": "X", "name": "ProcessMessages 660", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780475441, "dur": 54, "ph": "X", "name": "ReadAsync 660", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780475498, "dur": 1, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780475501, "dur": 46, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780475550, "dur": 2, "ph": "X", "name": "ProcessMessages 329", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780475553, "dur": 130, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780475687, "dur": 2, "ph": "X", "name": "ProcessMessages 1318", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780475691, "dur": 36, "ph": "X", "name": "ReadAsync 1318", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780475730, "dur": 1, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780475733, "dur": 72, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780475817, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780475819, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780475875, "dur": 1, "ph": "X", "name": "ProcessMessages 855", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780475877, "dur": 51, "ph": "X", "name": "ReadAsync 855", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780475931, "dur": 1, "ph": "X", "name": "ProcessMessages 452", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780475933, "dur": 57, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780475994, "dur": 1, "ph": "X", "name": "ProcessMessages 418", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780475997, "dur": 69, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780476069, "dur": 1, "ph": "X", "name": "ProcessMessages 637", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780476071, "dur": 49, "ph": "X", "name": "ReadAsync 637", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780476123, "dur": 1, "ph": "X", "name": "ProcessMessages 622", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780476126, "dur": 50, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780476179, "dur": 2, "ph": "X", "name": "ProcessMessages 693", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780476183, "dur": 45, "ph": "X", "name": "ReadAsync 693", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780476231, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780476233, "dur": 49, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780476286, "dur": 1, "ph": "X", "name": "ProcessMessages 558", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780476288, "dur": 34, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780476325, "dur": 38, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780476366, "dur": 1, "ph": "X", "name": "ProcessMessages 572", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780476368, "dur": 46, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780476416, "dur": 1, "ph": "X", "name": "ProcessMessages 441", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780476418, "dur": 44, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780476463, "dur": 1, "ph": "X", "name": "ProcessMessages 483", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780476465, "dur": 40, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780476507, "dur": 1, "ph": "X", "name": "ProcessMessages 569", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780476508, "dur": 36, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780476546, "dur": 1, "ph": "X", "name": "ProcessMessages 455", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780476548, "dur": 67, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780476622, "dur": 1, "ph": "X", "name": "ProcessMessages 382", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780476624, "dur": 65, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780476693, "dur": 2, "ph": "X", "name": "ProcessMessages 934", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780476696, "dur": 54, "ph": "X", "name": "ReadAsync 934", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780476752, "dur": 1, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780476754, "dur": 55, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780476813, "dur": 1, "ph": "X", "name": "ProcessMessages 595", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780476816, "dur": 42, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780476860, "dur": 1, "ph": "X", "name": "ProcessMessages 58", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780476863, "dur": 39, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780476905, "dur": 1, "ph": "X", "name": "ProcessMessages 252", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780476908, "dur": 59, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780476970, "dur": 2, "ph": "X", "name": "ProcessMessages 476", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780476974, "dur": 51, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780477028, "dur": 2, "ph": "X", "name": "ProcessMessages 598", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780477031, "dur": 40, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780477072, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780477075, "dur": 43, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780477122, "dur": 1, "ph": "X", "name": "ProcessMessages 316", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780477124, "dur": 49, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780477176, "dur": 2, "ph": "X", "name": "ProcessMessages 592", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780477180, "dur": 50, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780477232, "dur": 1, "ph": "X", "name": "ProcessMessages 668", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780477235, "dur": 58, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780477296, "dur": 1, "ph": "X", "name": "ProcessMessages 592", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780477298, "dur": 51, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780477352, "dur": 1, "ph": "X", "name": "ProcessMessages 662", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780477355, "dur": 42, "ph": "X", "name": "ReadAsync 662", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780477400, "dur": 1, "ph": "X", "name": "ProcessMessages 266", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780477404, "dur": 51, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780477457, "dur": 1, "ph": "X", "name": "ProcessMessages 586", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780477460, "dur": 46, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780477509, "dur": 1, "ph": "X", "name": "ProcessMessages 348", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780477512, "dur": 52, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780477567, "dur": 1, "ph": "X", "name": "ProcessMessages 551", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780477569, "dur": 37, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780477610, "dur": 1, "ph": "X", "name": "ProcessMessages 49", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780477612, "dur": 56, "ph": "X", "name": "ReadAsync 49", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780477672, "dur": 2, "ph": "X", "name": "ProcessMessages 728", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780477675, "dur": 53, "ph": "X", "name": "ReadAsync 728", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780477732, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780477735, "dur": 58, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780477797, "dur": 2, "ph": "X", "name": "ProcessMessages 632", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780477800, "dur": 57, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780477863, "dur": 1, "ph": "X", "name": "ProcessMessages 600", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780477865, "dur": 52, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780477920, "dur": 1, "ph": "X", "name": "ProcessMessages 716", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780477923, "dur": 61, "ph": "X", "name": "ReadAsync 716", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780477987, "dur": 1, "ph": "X", "name": "ProcessMessages 444", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780477990, "dur": 53, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780478046, "dur": 3, "ph": "X", "name": "ProcessMessages 768", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780478050, "dur": 53, "ph": "X", "name": "ReadAsync 768", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780478106, "dur": 1, "ph": "X", "name": "ProcessMessages 462", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780478108, "dur": 50, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780478161, "dur": 1, "ph": "X", "name": "ProcessMessages 626", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780478163, "dur": 51, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780478217, "dur": 1, "ph": "X", "name": "ProcessMessages 629", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780478219, "dur": 51, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780478273, "dur": 2, "ph": "X", "name": "ProcessMessages 617", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780478277, "dur": 81, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780478361, "dur": 1, "ph": "X", "name": "ProcessMessages 582", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780478363, "dur": 52, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780478419, "dur": 1, "ph": "X", "name": "ProcessMessages 326", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780478422, "dur": 51, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780478477, "dur": 2, "ph": "X", "name": "ProcessMessages 727", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780478480, "dur": 48, "ph": "X", "name": "ReadAsync 727", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780478532, "dur": 1, "ph": "X", "name": "ProcessMessages 165", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780478534, "dur": 36, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780478573, "dur": 1, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780478574, "dur": 29, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780478606, "dur": 1, "ph": "X", "name": "ProcessMessages 260", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780478609, "dur": 47, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780478660, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780478662, "dur": 59, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780478726, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780478728, "dur": 56, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780478789, "dur": 2, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780478793, "dur": 99, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780478898, "dur": 1, "ph": "X", "name": "ProcessMessages 453", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780478902, "dur": 47, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780478951, "dur": 1, "ph": "X", "name": "ProcessMessages 616", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780478954, "dur": 43, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780479001, "dur": 1, "ph": "X", "name": "ProcessMessages 165", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780479004, "dur": 56, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780479063, "dur": 1, "ph": "X", "name": "ProcessMessages 557", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780479066, "dur": 52, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780479122, "dur": 2, "ph": "X", "name": "ProcessMessages 452", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780479125, "dur": 46, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780479173, "dur": 1, "ph": "X", "name": "ProcessMessages 422", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780479176, "dur": 63, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780479244, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780479292, "dur": 1, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780479294, "dur": 48, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780479346, "dur": 1, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780479348, "dur": 49, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780479400, "dur": 1, "ph": "X", "name": "ProcessMessages 361", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780479403, "dur": 56, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780479462, "dur": 1, "ph": "X", "name": "ProcessMessages 670", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780479465, "dur": 49, "ph": "X", "name": "ReadAsync 670", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780479517, "dur": 1, "ph": "X", "name": "ProcessMessages 586", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780479520, "dur": 50, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780479572, "dur": 1, "ph": "X", "name": "ProcessMessages 449", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780479578, "dur": 74, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780479656, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780479658, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780479724, "dur": 2, "ph": "X", "name": "ProcessMessages 606", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780479727, "dur": 47, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780479776, "dur": 1, "ph": "X", "name": "ProcessMessages 480", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780479779, "dur": 27, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780479808, "dur": 1, "ph": "X", "name": "ProcessMessages 103", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780479811, "dur": 72, "ph": "X", "name": "ReadAsync 103", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780479887, "dur": 1, "ph": "X", "name": "ProcessMessages 370", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780479890, "dur": 46, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780479939, "dur": 2, "ph": "X", "name": "ProcessMessages 282", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780479943, "dur": 50, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780479996, "dur": 2, "ph": "X", "name": "ProcessMessages 456", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780480000, "dur": 49, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780480052, "dur": 1, "ph": "X", "name": "ProcessMessages 410", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780480055, "dur": 48, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780480105, "dur": 1, "ph": "X", "name": "ProcessMessages 410", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780480109, "dur": 49, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780480161, "dur": 1, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780480163, "dur": 39, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780480205, "dur": 1, "ph": "X", "name": "ProcessMessages 66", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780480207, "dur": 37, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780480246, "dur": 1, "ph": "X", "name": "ProcessMessages 672", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780480248, "dur": 43, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780480296, "dur": 58, "ph": "X", "name": "ReadAsync 199", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780480356, "dur": 2, "ph": "X", "name": "ProcessMessages 531", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780480359, "dur": 46, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780480409, "dur": 42, "ph": "X", "name": "ReadAsync 184", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780480454, "dur": 30, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780480487, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780480489, "dur": 45, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780480536, "dur": 1, "ph": "X", "name": "ProcessMessages 473", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780480538, "dur": 42, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780480584, "dur": 2, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780480587, "dur": 64, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780480654, "dur": 2, "ph": "X", "name": "ProcessMessages 551", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780480658, "dur": 50, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780480711, "dur": 1, "ph": "X", "name": "ProcessMessages 429", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780480714, "dur": 59, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780480778, "dur": 1, "ph": "X", "name": "ProcessMessages 438", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780480782, "dur": 54, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780480839, "dur": 3, "ph": "X", "name": "ProcessMessages 581", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780480843, "dur": 49, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780480895, "dur": 1, "ph": "X", "name": "ProcessMessages 569", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780480898, "dur": 54, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780480957, "dur": 2, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780480960, "dur": 53, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780481016, "dur": 1, "ph": "X", "name": "ProcessMessages 616", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780481018, "dur": 52, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780481074, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780481076, "dur": 50, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780481129, "dur": 1, "ph": "X", "name": "ProcessMessages 532", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780481131, "dur": 51, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780481186, "dur": 1, "ph": "X", "name": "ProcessMessages 337", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780481188, "dur": 51, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780481242, "dur": 1, "ph": "X", "name": "ProcessMessages 796", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780481244, "dur": 54, "ph": "X", "name": "ReadAsync 796", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780481301, "dur": 1, "ph": "X", "name": "ProcessMessages 726", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780481303, "dur": 40, "ph": "X", "name": "ReadAsync 726", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780481346, "dur": 1, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780481347, "dur": 38, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780481388, "dur": 1, "ph": "X", "name": "ProcessMessages 705", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780481389, "dur": 42, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780481434, "dur": 9, "ph": "X", "name": "ProcessMessages 524", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780481444, "dur": 44, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780481490, "dur": 1, "ph": "X", "name": "ProcessMessages 914", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780481492, "dur": 38, "ph": "X", "name": "ReadAsync 914", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780481532, "dur": 1, "ph": "X", "name": "ProcessMessages 592", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780481534, "dur": 73, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780481610, "dur": 2, "ph": "X", "name": "ProcessMessages 404", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780481613, "dur": 58, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780481674, "dur": 1, "ph": "X", "name": "ProcessMessages 894", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780481676, "dur": 44, "ph": "X", "name": "ReadAsync 894", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780481723, "dur": 1, "ph": "X", "name": "ProcessMessages 358", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780481726, "dur": 52, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780481781, "dur": 1, "ph": "X", "name": "ProcessMessages 477", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780481784, "dur": 51, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780481839, "dur": 2, "ph": "X", "name": "ProcessMessages 392", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780481842, "dur": 47, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780481892, "dur": 2, "ph": "X", "name": "ProcessMessages 377", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780481896, "dur": 53, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780481953, "dur": 2, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780481957, "dur": 52, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780482014, "dur": 2, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780482017, "dur": 51, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780482073, "dur": 2, "ph": "X", "name": "ProcessMessages 424", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780482076, "dur": 48, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780482127, "dur": 1, "ph": "X", "name": "ProcessMessages 409", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780482129, "dur": 49, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780482181, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780482184, "dur": 43, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780482230, "dur": 1, "ph": "X", "name": "ProcessMessages 233", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780482233, "dur": 54, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780482290, "dur": 1, "ph": "X", "name": "ProcessMessages 723", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780482293, "dur": 37, "ph": "X", "name": "ReadAsync 723", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780482332, "dur": 1, "ph": "X", "name": "ProcessMessages 356", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780482333, "dur": 33, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780482369, "dur": 47, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780482419, "dur": 1, "ph": "X", "name": "ProcessMessages 629", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780482421, "dur": 44, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780482467, "dur": 1, "ph": "X", "name": "ProcessMessages 595", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780482469, "dur": 44, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780482517, "dur": 1, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780482519, "dur": 40, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780482562, "dur": 1, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780482564, "dur": 229, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780482799, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780482856, "dur": 1, "ph": "X", "name": "ProcessMessages 962", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780482858, "dur": 49, "ph": "X", "name": "ReadAsync 962", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780482911, "dur": 2, "ph": "X", "name": "ProcessMessages 579", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780482915, "dur": 46, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780482965, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780482967, "dur": 46, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780483016, "dur": 1, "ph": "X", "name": "ProcessMessages 185", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780483018, "dur": 43, "ph": "X", "name": "ReadAsync 185", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780483065, "dur": 1, "ph": "X", "name": "ProcessMessages 53", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780483067, "dur": 28, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780483098, "dur": 39, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780483142, "dur": 1, "ph": "X", "name": "ProcessMessages 78", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780483145, "dur": 74, "ph": "X", "name": "ReadAsync 78", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780483223, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780483225, "dur": 78, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780483306, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780483309, "dur": 39, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780483352, "dur": 1, "ph": "X", "name": "ProcessMessages 214", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780483354, "dur": 42, "ph": "X", "name": "ReadAsync 214", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780483400, "dur": 1, "ph": "X", "name": "ProcessMessages 196", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780483403, "dur": 36, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780483447, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780483449, "dur": 135, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780483589, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780483591, "dur": 50, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780483645, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780483647, "dur": 144, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780483795, "dur": 1, "ph": "X", "name": "ProcessMessages 1", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780483797, "dur": 58, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780483859, "dur": 2, "ph": "X", "name": "ProcessMessages 733", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780483862, "dur": 112, "ph": "X", "name": "ReadAsync 733", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780483978, "dur": 3, "ph": "X", "name": "ProcessMessages 105", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780483983, "dur": 81, "ph": "X", "name": "ReadAsync 105", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780484068, "dur": 1, "ph": "X", "name": "ProcessMessages 185", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780484071, "dur": 38, "ph": "X", "name": "ReadAsync 185", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780484113, "dur": 1, "ph": "X", "name": "ProcessMessages 193", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780484115, "dur": 39, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780484158, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780484160, "dur": 29, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780484192, "dur": 1, "ph": "X", "name": "ProcessMessages 118", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780484195, "dur": 36, "ph": "X", "name": "ReadAsync 118", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780484235, "dur": 1, "ph": "X", "name": "ProcessMessages 236", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780484237, "dur": 58, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780484299, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780484301, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780484344, "dur": 1, "ph": "X", "name": "ProcessMessages 516", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780484347, "dur": 51, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780484401, "dur": 1, "ph": "X", "name": "ProcessMessages 114", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780484403, "dur": 48, "ph": "X", "name": "ReadAsync 114", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780484454, "dur": 1, "ph": "X", "name": "ProcessMessages 497", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780484457, "dur": 42, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780484501, "dur": 1, "ph": "X", "name": "ProcessMessages 314", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780484503, "dur": 44, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780484551, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780484553, "dur": 41, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780484597, "dur": 1, "ph": "X", "name": "ProcessMessages 541", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780484600, "dur": 33, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780484636, "dur": 1, "ph": "X", "name": "ProcessMessages 153", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780484639, "dur": 42, "ph": "X", "name": "ReadAsync 153", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780484684, "dur": 1, "ph": "X", "name": "ProcessMessages 392", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780484687, "dur": 26, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780484716, "dur": 53, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780484774, "dur": 35, "ph": "X", "name": "ReadAsync 6", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780484812, "dur": 1, "ph": "X", "name": "ProcessMessages 326", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780484815, "dur": 37, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780484855, "dur": 1, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780484858, "dur": 44, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780484905, "dur": 1, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780484908, "dur": 47, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780484958, "dur": 2, "ph": "X", "name": "ProcessMessages 516", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780484961, "dur": 58, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780485022, "dur": 1, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780485024, "dur": 46, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780485075, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780485131, "dur": 1, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780485134, "dur": 50, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780485187, "dur": 1, "ph": "X", "name": "ProcessMessages 507", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780485189, "dur": 44, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780485236, "dur": 1, "ph": "X", "name": "ProcessMessages 356", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780485239, "dur": 50, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780485292, "dur": 3, "ph": "X", "name": "ProcessMessages 754", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780485305, "dur": 49, "ph": "X", "name": "ReadAsync 754", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780485357, "dur": 1, "ph": "X", "name": "ProcessMessages 653", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780485360, "dur": 51, "ph": "X", "name": "ReadAsync 653", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780485414, "dur": 1, "ph": "X", "name": "ProcessMessages 551", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780485417, "dur": 34, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780485454, "dur": 1, "ph": "X", "name": "ProcessMessages 210", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780485457, "dur": 47, "ph": "X", "name": "ReadAsync 210", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780485506, "dur": 1, "ph": "X", "name": "ProcessMessages 541", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780485510, "dur": 49, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780485562, "dur": 1, "ph": "X", "name": "ProcessMessages 638", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780485567, "dur": 49, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780485619, "dur": 2, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780485622, "dur": 39, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780485663, "dur": 1, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780485666, "dur": 42, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780485710, "dur": 1, "ph": "X", "name": "ProcessMessages 314", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780485712, "dur": 39, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780485754, "dur": 1, "ph": "X", "name": "ProcessMessages 422", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780485756, "dur": 47, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780485806, "dur": 1, "ph": "X", "name": "ProcessMessages 612", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780485808, "dur": 51, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780485862, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780485865, "dur": 45, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780485914, "dur": 2, "ph": "X", "name": "ProcessMessages 519", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780485917, "dur": 47, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780485967, "dur": 9, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780485977, "dur": 52, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780486032, "dur": 1, "ph": "X", "name": "ProcessMessages 797", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780486035, "dur": 49, "ph": "X", "name": "ReadAsync 797", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780486086, "dur": 1, "ph": "X", "name": "ProcessMessages 516", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780486089, "dur": 44, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780486136, "dur": 1, "ph": "X", "name": "ProcessMessages 332", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780486138, "dur": 53, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780486194, "dur": 1, "ph": "X", "name": "ProcessMessages 744", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780486196, "dur": 47, "ph": "X", "name": "ReadAsync 744", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780486246, "dur": 1, "ph": "X", "name": "ProcessMessages 646", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780486249, "dur": 51, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780486304, "dur": 1, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780486306, "dur": 47, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780486357, "dur": 1, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780486360, "dur": 53, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780486416, "dur": 1, "ph": "X", "name": "ProcessMessages 332", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780486418, "dur": 46, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780486468, "dur": 1, "ph": "X", "name": "ProcessMessages 422", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780486470, "dur": 49, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780486522, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780486525, "dur": 47, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780486574, "dur": 1, "ph": "X", "name": "ProcessMessages 558", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780486577, "dur": 57, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780486636, "dur": 1, "ph": "X", "name": "ProcessMessages 595", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780486639, "dur": 44, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780486686, "dur": 2, "ph": "X", "name": "ProcessMessages 444", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780486689, "dur": 64, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780486756, "dur": 1, "ph": "X", "name": "ProcessMessages 253", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780486759, "dur": 51, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780486813, "dur": 3, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780486818, "dur": 43, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780486865, "dur": 1, "ph": "X", "name": "ProcessMessages 163", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780486867, "dur": 51, "ph": "X", "name": "ReadAsync 163", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780486920, "dur": 1, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780486923, "dur": 52, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780486978, "dur": 1, "ph": "X", "name": "ProcessMessages 695", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780486980, "dur": 46, "ph": "X", "name": "ReadAsync 695", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780487029, "dur": 1, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780487031, "dur": 52, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780487087, "dur": 1, "ph": "X", "name": "ProcessMessages 488", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780487089, "dur": 43, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780487135, "dur": 1, "ph": "X", "name": "ProcessMessages 536", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780487138, "dur": 52, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780487194, "dur": 1, "ph": "X", "name": "ProcessMessages 624", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780487196, "dur": 48, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780487247, "dur": 10, "ph": "X", "name": "ProcessMessages 555", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780487260, "dur": 50, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780487312, "dur": 1, "ph": "X", "name": "ProcessMessages 761", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780487315, "dur": 38, "ph": "X", "name": "ReadAsync 761", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780487357, "dur": 1, "ph": "X", "name": "ProcessMessages 333", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780487359, "dur": 43, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780487406, "dur": 1, "ph": "X", "name": "ProcessMessages 302", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780487408, "dur": 49, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780487460, "dur": 1, "ph": "X", "name": "ProcessMessages 382", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780487462, "dur": 48, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780487514, "dur": 1, "ph": "X", "name": "ProcessMessages 479", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780487516, "dur": 50, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780487568, "dur": 3, "ph": "X", "name": "ProcessMessages 566", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780487572, "dur": 45, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780487622, "dur": 2, "ph": "X", "name": "ProcessMessages 344", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780487625, "dur": 47, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780487675, "dur": 1, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780487677, "dur": 50, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780487731, "dur": 1, "ph": "X", "name": "ProcessMessages 434", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780487734, "dur": 47, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780487784, "dur": 2, "ph": "X", "name": "ProcessMessages 452", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780487787, "dur": 46, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780487836, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780487838, "dur": 46, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780487888, "dur": 2, "ph": "X", "name": "ProcessMessages 270", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780487891, "dur": 49, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780487943, "dur": 1, "ph": "X", "name": "ProcessMessages 416", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780487946, "dur": 52, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780488001, "dur": 1, "ph": "X", "name": "ProcessMessages 477", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780488004, "dur": 50, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780488057, "dur": 1, "ph": "X", "name": "ProcessMessages 568", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780488061, "dur": 48, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780488113, "dur": 1, "ph": "X", "name": "ProcessMessages 504", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780488115, "dur": 53, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780488171, "dur": 2, "ph": "X", "name": "ProcessMessages 718", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780488175, "dur": 50, "ph": "X", "name": "ReadAsync 718", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780488228, "dur": 1, "ph": "X", "name": "ProcessMessages 443", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780488231, "dur": 51, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780488284, "dur": 1, "ph": "X", "name": "ProcessMessages 501", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780488287, "dur": 50, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780488340, "dur": 1, "ph": "X", "name": "ProcessMessages 646", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780488343, "dur": 57, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780488404, "dur": 2, "ph": "X", "name": "ProcessMessages 509", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780488407, "dur": 50, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780488460, "dur": 1, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780488463, "dur": 50, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780488516, "dur": 2, "ph": "X", "name": "ProcessMessages 433", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780488519, "dur": 54, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780488576, "dur": 1, "ph": "X", "name": "ProcessMessages 433", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780488579, "dur": 49, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780488631, "dur": 1, "ph": "X", "name": "ProcessMessages 488", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780488634, "dur": 34, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780488670, "dur": 2, "ph": "X", "name": "ProcessMessages 431", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780488673, "dur": 32, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780488708, "dur": 1, "ph": "X", "name": "ProcessMessages 213", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780488711, "dur": 52, "ph": "X", "name": "ReadAsync 213", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780488766, "dur": 1, "ph": "X", "name": "ProcessMessages 350", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780488768, "dur": 40, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780488812, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780488816, "dur": 50, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780488868, "dur": 1, "ph": "X", "name": "ProcessMessages 347", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780488870, "dur": 53, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780488927, "dur": 3, "ph": "X", "name": "ProcessMessages 567", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780488931, "dur": 49, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780488991, "dur": 1, "ph": "X", "name": "ProcessMessages 584", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780488994, "dur": 45, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780489042, "dur": 1, "ph": "X", "name": "ProcessMessages 297", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780489045, "dur": 47, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780489094, "dur": 1, "ph": "X", "name": "ProcessMessages 610", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780489097, "dur": 52, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780489152, "dur": 2, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780489155, "dur": 66, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780489224, "dur": 2, "ph": "X", "name": "ProcessMessages 856", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780489227, "dur": 51, "ph": "X", "name": "ReadAsync 856", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780489280, "dur": 2, "ph": "X", "name": "ProcessMessages 674", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780489283, "dur": 49, "ph": "X", "name": "ReadAsync 674", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780489335, "dur": 1, "ph": "X", "name": "ProcessMessages 582", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780489337, "dur": 44, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780489384, "dur": 1, "ph": "X", "name": "ProcessMessages 373", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780489386, "dur": 48, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780489437, "dur": 1, "ph": "X", "name": "ProcessMessages 531", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780489440, "dur": 48, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780489491, "dur": 1, "ph": "X", "name": "ProcessMessages 408", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780489492, "dur": 38, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780489532, "dur": 1, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780489535, "dur": 39, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780489576, "dur": 1, "ph": "X", "name": "ProcessMessages 387", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780489578, "dur": 55, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780489637, "dur": 2, "ph": "X", "name": "ProcessMessages 384", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780489640, "dur": 62, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780489706, "dur": 1, "ph": "X", "name": "ProcessMessages 572", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780489708, "dur": 51, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780489764, "dur": 2, "ph": "X", "name": "ProcessMessages 332", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780489767, "dur": 60, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780489831, "dur": 1, "ph": "X", "name": "ProcessMessages 453", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780489833, "dur": 53, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780489890, "dur": 1, "ph": "X", "name": "ProcessMessages 419", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780489893, "dur": 51, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780489946, "dur": 1, "ph": "X", "name": "ProcessMessages 431", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780489949, "dur": 52, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780490004, "dur": 3, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780490009, "dur": 48, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780490060, "dur": 1, "ph": "X", "name": "ProcessMessages 528", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780490063, "dur": 48, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780490114, "dur": 1, "ph": "X", "name": "ProcessMessages 354", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780490116, "dur": 51, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780490170, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780490172, "dur": 50, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780490226, "dur": 1, "ph": "X", "name": "ProcessMessages 389", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780490230, "dur": 51, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780490284, "dur": 2, "ph": "X", "name": "ProcessMessages 399", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780490287, "dur": 58, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780490348, "dur": 2, "ph": "X", "name": "ProcessMessages 494", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780490351, "dur": 52, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780490407, "dur": 1, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780490409, "dur": 41, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780490452, "dur": 1, "ph": "X", "name": "ProcessMessages 324", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780490455, "dur": 45, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780490504, "dur": 1, "ph": "X", "name": "ProcessMessages 215", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780490506, "dur": 144, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780490654, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780490657, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780490695, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780490698, "dur": 37, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780490739, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780490742, "dur": 32, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780490777, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780490780, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780490816, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780490818, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780490854, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780490856, "dur": 30, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780490889, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780490891, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780490930, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780490933, "dur": 53, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780490989, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780490992, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780491030, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780491032, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780491077, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780491080, "dur": 37, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780491122, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780491124, "dur": 32, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780491162, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780491165, "dur": 36, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780491204, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780491207, "dur": 44, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780491254, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780491256, "dur": 37, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780491297, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780491301, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780491342, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780491345, "dur": 37, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780491385, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780491387, "dur": 49, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780491440, "dur": 2, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780491443, "dur": 37, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780491483, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780491486, "dur": 32, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780491521, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780491523, "dur": 36, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780491562, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780491564, "dur": 41, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780491608, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780491614, "dur": 43, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780491664, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780491666, "dur": 37, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780491707, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780491709, "dur": 32, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780491745, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780491747, "dur": 31, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780491781, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780491783, "dur": 38, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780491825, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780491828, "dur": 37, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780491868, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780491871, "dur": 35, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780491908, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780491911, "dur": 47, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780491961, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780491964, "dur": 60, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780492027, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780492029, "dur": 33, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780492066, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780492068, "dur": 35, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780492107, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780492110, "dur": 38, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780492151, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780492154, "dur": 35, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780492193, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780492195, "dur": 34, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780492233, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780492235, "dur": 38, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780492277, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780492279, "dur": 33, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780492316, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780492318, "dur": 30, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780492351, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780492353, "dur": 33, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780492389, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780492392, "dur": 37, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780492431, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780492434, "dur": 32, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780492477, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780492479, "dur": 255, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780492738, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780492741, "dur": 48, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780492792, "dur": 5, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780492799, "dur": 34, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780492837, "dur": 9, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780492847, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780492889, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780492893, "dur": 37, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780492934, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780492936, "dur": 52, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780492991, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780492994, "dur": 42, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780493040, "dur": 3, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780493044, "dur": 42, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780493090, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780493094, "dur": 42, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780493140, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780493144, "dur": 38, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780493186, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780493188, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780493227, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780493230, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780493269, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780493272, "dur": 88, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780493364, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780493367, "dur": 45, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780493416, "dur": 2, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780493419, "dur": 40, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780493463, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780493465, "dur": 38, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780493508, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780493510, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780493550, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780493553, "dur": 63, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780493620, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780493623, "dur": 41, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780493678, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780493681, "dur": 33, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780493717, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780493720, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780493764, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780493767, "dur": 97, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780493868, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780493871, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780493893, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780493933, "dur": 111, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780494048, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780494051, "dur": 795, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780494851, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780494856, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780494905, "dur": 868, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780495782, "dur": 76, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780495862, "dur": 2746, "ph": "X", "name": "ProcessMessages 46", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780498617, "dur": 67, "ph": "X", "name": "ReadAsync 46", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780498689, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780498693, "dur": 1605, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780500305, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780500307, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780500358, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780500360, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780500405, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780500409, "dur": 65, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780500478, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780500480, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780500516, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780500518, "dur": 148, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780500670, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780500672, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780500712, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780500715, "dur": 4496, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780505225, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780505228, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780505285, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780505288, "dur": 86, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780505378, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780505384, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780505435, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780505438, "dur": 347, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780505789, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780505794, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780505841, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780505844, "dur": 39, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780505885, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780505887, "dur": 156, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780506047, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780506049, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780506089, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780506092, "dur": 69, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780506165, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780506198, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780506200, "dur": 454, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780506658, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780506660, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780506702, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780506705, "dur": 41, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780506750, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780506752, "dur": 61, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780506817, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780506819, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780506855, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780506891, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780506893, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780506927, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780506936, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780506971, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780506973, "dur": 48, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780507024, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780507027, "dur": 33, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780507063, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780507065, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780507104, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780507140, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780507143, "dur": 41, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780507189, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780507222, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780507224, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780507274, "dur": 117, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780507395, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780507430, "dur": 42, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780507478, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780507510, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780507544, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780507584, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780507587, "dur": 110, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780507703, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780507741, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780507743, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780507780, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780507783, "dur": 78, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780507865, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780507897, "dur": 381, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780508282, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780508284, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780508325, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780508328, "dur": 34, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780508366, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780508415, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780508418, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780508454, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780508456, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780508493, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780508496, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780508531, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780508534, "dur": 65, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780508602, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780508604, "dur": 34, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780508641, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780508644, "dur": 124, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780508771, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780508774, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780508825, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780508828, "dur": 356, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780509189, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780509239, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780509241, "dur": 78, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780509322, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780509324, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780509368, "dur": 48, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780509420, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780509422, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780509460, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780509496, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780509497, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780509538, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780509541, "dur": 77, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780509621, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780509624, "dur": 111, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780509739, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780509741, "dur": 155, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780509900, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780509903, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780509949, "dur": 4, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780509955, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780510002, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780510005, "dur": 67, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780510075, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780510077, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780510119, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780510121, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780510163, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780510165, "dur": 42, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780510211, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780510215, "dur": 36, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780510255, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780510257, "dur": 37, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780510297, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780510299, "dur": 34, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780510336, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780510339, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780510377, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780510380, "dur": 35, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780510418, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780510421, "dur": 37, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780510461, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780510464, "dur": 35, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780510504, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780510506, "dur": 46, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780510554, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780510558, "dur": 29, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780510589, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780510591, "dur": 148, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780510742, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780510744, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780510785, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780510787, "dur": 35, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780510827, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780510829, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780510876, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780510878, "dur": 888, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780511770, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780511773, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780511814, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780511817, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780511860, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780511863, "dur": 173, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780512039, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780512041, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780512090, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780512093, "dur": 46, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780512142, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780512144, "dur": 80, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780512230, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780512276, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780512279, "dur": 329, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780512614, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780512663, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780512665, "dur": 45, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780512714, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780512717, "dur": 148, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780512871, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780512910, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780512912, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780512959, "dur": 23, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780512984, "dur": 95, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780513084, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780513133, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780513136, "dur": 290, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780513429, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780513432, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780513477, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780513480, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780513518, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780513520, "dur": 36, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780513559, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780513561, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780513603, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780513605, "dur": 67, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780513676, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780513678, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780513721, "dur": 414, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780514150, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780514152, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780514182, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780514184, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780514199, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780514241, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780514243, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780514289, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780514293, "dur": 126, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780514422, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780514424, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780514470, "dur": 1965, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780516441, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780516445, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780516503, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899780516508, "dur": 2145579, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899782662097, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899782662102, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899782662156, "dur": 1, "ph": "X", "name": "ProcessMessages 162", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899782662168, "dur": 43, "ph": "X", "name": "ReadAsync 162", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899782662214, "dur": 22, "ph": "X", "name": "ProcessMessages 157", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899782662238, "dur": 2006, "ph": "X", "name": "ReadAsync 157", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899782664253, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899782664256, "dur": 68, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899782664329, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899782664333, "dur": 2011, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899782666352, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899782666356, "dur": 73, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899782666434, "dur": 37, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899782666473, "dur": 15962, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899782682447, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899782682451, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899782682511, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899782682517, "dur": 1037, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899782683620, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899782683624, "dur": 187, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899782683817, "dur": 33, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899782683852, "dur": 373, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899782684230, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899782684232, "dur": 107, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899782684343, "dur": 1, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751899782684346, "dur": 7272, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 22320, "tid": 555, "ts": 1751899782708082, "dur": 1412, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 22320, "tid": 17179869184, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 22320, "tid": 17179869184, "ts": 1751899780382581, "dur": 46, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 22320, "tid": 17179869184, "ts": 1751899780382628, "dur": 24796, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 22320, "tid": 17179869184, "ts": 1751899780407425, "dur": 262, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 22320, "tid": 555, "ts": 1751899782709496, "dur": 6, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 22320, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 22320, "tid": 1, "ts": 1751899779373026, "dur": 5846, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 22320, "tid": 1, "ts": 1751899779378877, "dur": 52685, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 22320, "tid": 1, "ts": 1751899779431572, "dur": 46926, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 22320, "tid": 555, "ts": 1751899782709504, "dur": 4, "ph": "X", "name": "", "args": {}}, {"pid": 22320, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779370546, "dur": 13079, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779383628, "dur": 106119, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779384653, "dur": 3375, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779388034, "dur": 2461, "ph": "X", "name": "ProcessMessages 20496", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779390500, "dur": 364, "ph": "X", "name": "ReadAsync 20496", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779390868, "dur": 49, "ph": "X", "name": "ProcessMessages 20525", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779390918, "dur": 126, "ph": "X", "name": "ReadAsync 20525", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779391047, "dur": 1, "ph": "X", "name": "ProcessMessages 828", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779391049, "dur": 38, "ph": "X", "name": "ReadAsync 828", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779391089, "dur": 26, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779391118, "dur": 84, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779391206, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779391248, "dur": 34, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779391285, "dur": 77, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779391365, "dur": 1, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779391368, "dur": 40, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779391409, "dur": 1, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779391411, "dur": 34, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779391448, "dur": 29, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779391480, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779391517, "dur": 20, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779391540, "dur": 34, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779391576, "dur": 94, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779391673, "dur": 1, "ph": "X", "name": "ProcessMessages 308", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779391676, "dur": 42, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779391720, "dur": 1, "ph": "X", "name": "ProcessMessages 755", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779391722, "dur": 42, "ph": "X", "name": "ReadAsync 755", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779391767, "dur": 102, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779391873, "dur": 1, "ph": "X", "name": "ProcessMessages 661", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779391875, "dur": 53, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779391930, "dur": 1, "ph": "X", "name": "ProcessMessages 765", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779391931, "dur": 40, "ph": "X", "name": "ReadAsync 765", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779391975, "dur": 35, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779392013, "dur": 44, "ph": "X", "name": "ReadAsync 127", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779392059, "dur": 1, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779392060, "dur": 43, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779392106, "dur": 1, "ph": "X", "name": "ProcessMessages 422", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779392108, "dur": 44, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779392154, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779392156, "dur": 37, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779392196, "dur": 40, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779392240, "dur": 43, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779392286, "dur": 1, "ph": "X", "name": "ProcessMessages 641", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779392288, "dur": 41, "ph": "X", "name": "ReadAsync 641", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779392332, "dur": 1, "ph": "X", "name": "ProcessMessages 519", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779392333, "dur": 41, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779392377, "dur": 37, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779392417, "dur": 1, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779392418, "dur": 39, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779392461, "dur": 43, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779392506, "dur": 1, "ph": "X", "name": "ProcessMessages 561", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779392507, "dur": 41, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779392551, "dur": 1, "ph": "X", "name": "ProcessMessages 513", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779392552, "dur": 44, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779392598, "dur": 1, "ph": "X", "name": "ProcessMessages 386", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779392600, "dur": 36, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779392639, "dur": 40, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779392682, "dur": 42, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779392726, "dur": 1, "ph": "X", "name": "ProcessMessages 630", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779392728, "dur": 42, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779392773, "dur": 1, "ph": "X", "name": "ProcessMessages 649", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779392775, "dur": 56, "ph": "X", "name": "ReadAsync 649", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779392834, "dur": 36, "ph": "X", "name": "ReadAsync 684", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779392874, "dur": 37, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779392913, "dur": 8, "ph": "X", "name": "ProcessMessages 729", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779392921, "dur": 37, "ph": "X", "name": "ReadAsync 729", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779392961, "dur": 1, "ph": "X", "name": "ProcessMessages 762", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779392962, "dur": 35, "ph": "X", "name": "ReadAsync 762", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779393001, "dur": 35, "ph": "X", "name": "ReadAsync 669", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779393039, "dur": 28, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779393070, "dur": 45, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779393118, "dur": 1, "ph": "X", "name": "ProcessMessages 534", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779393120, "dur": 43, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779393166, "dur": 1, "ph": "X", "name": "ProcessMessages 359", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779393168, "dur": 42, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779393212, "dur": 1, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779393214, "dur": 35, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779393252, "dur": 27, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779393282, "dur": 29, "ph": "X", "name": "ReadAsync 151", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779393316, "dur": 36, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779393355, "dur": 33, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779393389, "dur": 1, "ph": "X", "name": "ProcessMessages 299", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779393391, "dur": 40, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779393434, "dur": 1, "ph": "X", "name": "ProcessMessages 339", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779393436, "dur": 43, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779393482, "dur": 1, "ph": "X", "name": "ProcessMessages 416", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779393485, "dur": 39, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779393527, "dur": 36, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779393567, "dur": 32, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779393601, "dur": 1, "ph": "X", "name": "ProcessMessages 136", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779393603, "dur": 35, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779393639, "dur": 1, "ph": "X", "name": "ProcessMessages 424", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779393641, "dur": 37, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779393681, "dur": 29, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779393714, "dur": 37, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779393755, "dur": 45, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779393802, "dur": 8, "ph": "X", "name": "ProcessMessages 531", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779393811, "dur": 41, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779393855, "dur": 1, "ph": "X", "name": "ProcessMessages 718", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779393857, "dur": 34, "ph": "X", "name": "ReadAsync 718", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779393893, "dur": 1, "ph": "X", "name": "ProcessMessages 379", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779393895, "dur": 30, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779393928, "dur": 208, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779394141, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779394182, "dur": 1, "ph": "X", "name": "ProcessMessages 626", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779394184, "dur": 31, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779394217, "dur": 1, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779394219, "dur": 36, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779394258, "dur": 34, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779394294, "dur": 35, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779394332, "dur": 2, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779394335, "dur": 44, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779394383, "dur": 38, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779394425, "dur": 34, "ph": "X", "name": "ReadAsync 698", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779394460, "dur": 1, "ph": "X", "name": "ProcessMessages 354", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779394462, "dur": 33, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779394498, "dur": 35, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779394536, "dur": 24, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779394563, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779394597, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779394599, "dur": 40, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779394643, "dur": 31, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779394677, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779394714, "dur": 1, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779394716, "dur": 33, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779394752, "dur": 33, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779394788, "dur": 27, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779394819, "dur": 41, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779394862, "dur": 1, "ph": "X", "name": "ProcessMessages 437", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779394864, "dur": 46, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779394913, "dur": 1, "ph": "X", "name": "ProcessMessages 653", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779394915, "dur": 41, "ph": "X", "name": "ReadAsync 653", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779394958, "dur": 1, "ph": "X", "name": "ProcessMessages 543", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779394960, "dur": 36, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779394999, "dur": 28, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779395030, "dur": 35, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779395068, "dur": 28, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779395105, "dur": 44, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779395151, "dur": 1, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779395153, "dur": 40, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779395195, "dur": 1, "ph": "X", "name": "ProcessMessages 568", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779395197, "dur": 31, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779395231, "dur": 34, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779395268, "dur": 35, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779395306, "dur": 33, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779395342, "dur": 38, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779395383, "dur": 1, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779395385, "dur": 44, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779395432, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779395435, "dur": 29, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779395469, "dur": 36, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779395508, "dur": 29, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779395540, "dur": 34, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779395578, "dur": 33, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779395613, "dur": 1, "ph": "X", "name": "ProcessMessages 350", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779395615, "dur": 42, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779395659, "dur": 1, "ph": "X", "name": "ProcessMessages 417", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779395661, "dur": 35, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779395699, "dur": 1, "ph": "X", "name": "ProcessMessages 188", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779395701, "dur": 41, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779395745, "dur": 1, "ph": "X", "name": "ProcessMessages 748", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779395747, "dur": 37, "ph": "X", "name": "ReadAsync 748", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779395787, "dur": 33, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779395821, "dur": 1, "ph": "X", "name": "ProcessMessages 394", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779395823, "dur": 32, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779395858, "dur": 34, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779395894, "dur": 1, "ph": "X", "name": "ProcessMessages 204", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779395896, "dur": 32, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779395932, "dur": 39, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779395973, "dur": 1, "ph": "X", "name": "ProcessMessages 436", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779395975, "dur": 42, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779396020, "dur": 1, "ph": "X", "name": "ProcessMessages 422", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779396022, "dur": 40, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779396064, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779396066, "dur": 31, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779396098, "dur": 1, "ph": "X", "name": "ProcessMessages 294", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779396100, "dur": 41, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779396144, "dur": 32, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779396179, "dur": 31, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779396214, "dur": 31, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779396247, "dur": 1, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779396248, "dur": 32, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779396284, "dur": 29, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779396317, "dur": 30, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779396350, "dur": 53, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779396406, "dur": 1, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779396409, "dur": 53, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779396464, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779396467, "dur": 45, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779396514, "dur": 1, "ph": "X", "name": "ProcessMessages 430", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779396516, "dur": 32, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779396552, "dur": 33, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779396587, "dur": 34, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779396624, "dur": 33, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779396659, "dur": 1, "ph": "X", "name": "ProcessMessages 332", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779396661, "dur": 33, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779396698, "dur": 33, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779396734, "dur": 26, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779396764, "dur": 29, "ph": "X", "name": "ReadAsync 69", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779396795, "dur": 30, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779396828, "dur": 34, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779396865, "dur": 32, "ph": "X", "name": "ReadAsync 627", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779396900, "dur": 32, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779396935, "dur": 29, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779396967, "dur": 31, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779397001, "dur": 34, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779397039, "dur": 32, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779397074, "dur": 30, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779397107, "dur": 36, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779397146, "dur": 29, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779397178, "dur": 31, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779397212, "dur": 31, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779397246, "dur": 31, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779397280, "dur": 68, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779397352, "dur": 1, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779397354, "dur": 47, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779397404, "dur": 2, "ph": "X", "name": "ProcessMessages 724", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779397407, "dur": 50, "ph": "X", "name": "ReadAsync 724", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779397460, "dur": 1, "ph": "X", "name": "ProcessMessages 408", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779397462, "dur": 45, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779397510, "dur": 1, "ph": "X", "name": "ProcessMessages 400", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779397512, "dur": 36, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779397550, "dur": 1, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779397552, "dur": 34, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779397590, "dur": 32, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779397624, "dur": 1, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779397626, "dur": 29, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779397658, "dur": 45, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779397705, "dur": 1, "ph": "X", "name": "ProcessMessages 309", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779397707, "dur": 41, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779397751, "dur": 1, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779397753, "dur": 37, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779397792, "dur": 1, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779397794, "dur": 35, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779397831, "dur": 1, "ph": "X", "name": "ProcessMessages 379", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779397832, "dur": 43, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779397878, "dur": 1, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779397880, "dur": 45, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779397927, "dur": 1, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779397929, "dur": 39, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779397971, "dur": 1, "ph": "X", "name": "ProcessMessages 482", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779397973, "dur": 35, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779398010, "dur": 1, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779398011, "dur": 34, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779398048, "dur": 42, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779398093, "dur": 1, "ph": "X", "name": "ProcessMessages 383", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779398095, "dur": 43, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779398140, "dur": 1, "ph": "X", "name": "ProcessMessages 684", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779398142, "dur": 35, "ph": "X", "name": "ReadAsync 684", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779398181, "dur": 32, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779398216, "dur": 34, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779398253, "dur": 75, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779398331, "dur": 1, "ph": "X", "name": "ProcessMessages 269", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779398334, "dur": 48, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779398385, "dur": 11, "ph": "X", "name": "ProcessMessages 942", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779398398, "dur": 48, "ph": "X", "name": "ReadAsync 942", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779398451, "dur": 1, "ph": "X", "name": "ProcessMessages 483", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779398455, "dur": 39, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779398497, "dur": 1, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779398499, "dur": 43, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779398546, "dur": 45, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779398594, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779398596, "dur": 53, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779398653, "dur": 1, "ph": "X", "name": "ProcessMessages 697", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779398656, "dur": 48, "ph": "X", "name": "ReadAsync 697", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779398707, "dur": 1, "ph": "X", "name": "ProcessMessages 348", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779398710, "dur": 43, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779398756, "dur": 1, "ph": "X", "name": "ProcessMessages 545", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779398758, "dur": 39, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779398801, "dur": 1, "ph": "X", "name": "ProcessMessages 85", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779398804, "dur": 48, "ph": "X", "name": "ReadAsync 85", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779398855, "dur": 1, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779398857, "dur": 46, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779398907, "dur": 1, "ph": "X", "name": "ProcessMessages 519", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779398910, "dur": 47, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779398960, "dur": 1, "ph": "X", "name": "ProcessMessages 591", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779398964, "dur": 49, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779399016, "dur": 1, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779399018, "dur": 43, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779399064, "dur": 1, "ph": "X", "name": "ProcessMessages 543", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779399066, "dur": 36, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779399105, "dur": 1, "ph": "X", "name": "ProcessMessages 83", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779399107, "dur": 52, "ph": "X", "name": "ReadAsync 83", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779399162, "dur": 1, "ph": "X", "name": "ProcessMessages 635", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779399165, "dur": 50, "ph": "X", "name": "ReadAsync 635", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779399218, "dur": 1, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779399221, "dur": 49, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779399272, "dur": 1, "ph": "X", "name": "ProcessMessages 455", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779399275, "dur": 45, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779399322, "dur": 1, "ph": "X", "name": "ProcessMessages 545", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779399325, "dur": 44, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779399372, "dur": 1, "ph": "X", "name": "ProcessMessages 314", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779399375, "dur": 46, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779399424, "dur": 1, "ph": "X", "name": "ProcessMessages 528", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779399426, "dur": 45, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779399474, "dur": 1, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779399476, "dur": 51, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779399530, "dur": 1, "ph": "X", "name": "ProcessMessages 559", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779399534, "dur": 45, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779399581, "dur": 1, "ph": "X", "name": "ProcessMessages 431", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779399583, "dur": 46, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779399632, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779399634, "dur": 48, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779399685, "dur": 1, "ph": "X", "name": "ProcessMessages 650", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779399687, "dur": 37, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779399728, "dur": 35, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779399767, "dur": 34, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779399803, "dur": 1, "ph": "X", "name": "ProcessMessages 383", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779399805, "dur": 36, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779399844, "dur": 33, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779399879, "dur": 1, "ph": "X", "name": "ProcessMessages 350", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779399881, "dur": 39, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779399922, "dur": 1, "ph": "X", "name": "ProcessMessages 409", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779399924, "dur": 34, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779399962, "dur": 30, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779399995, "dur": 32, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779400031, "dur": 37, "ph": "X", "name": "ReadAsync 158", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779400071, "dur": 1, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779400073, "dur": 193, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779400271, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779400273, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779400324, "dur": 407, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779400735, "dur": 82, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779400821, "dur": 10, "ph": "X", "name": "ProcessMessages 1088", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779400832, "dur": 40, "ph": "X", "name": "ReadAsync 1088", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779400876, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779400878, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779400915, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779400917, "dur": 31, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779400951, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779400953, "dur": 33, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779400989, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779400993, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779401025, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779401027, "dur": 32, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779401062, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779401064, "dur": 39, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779401107, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779401110, "dur": 45, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779401158, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779401160, "dur": 31, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779401195, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779401197, "dur": 29, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779401230, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779401232, "dur": 29, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779401264, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779401266, "dur": 30, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779401299, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779401301, "dur": 31, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779401335, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779401338, "dur": 28, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779401368, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779401370, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779401405, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779401407, "dur": 29, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779401439, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779401441, "dur": 27, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779401471, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779401474, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779401505, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779401507, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779401541, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779401543, "dur": 28, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779401573, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779401575, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779401605, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779401607, "dur": 493, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779402104, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779402107, "dur": 200, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779402310, "dur": 7, "ph": "X", "name": "ProcessMessages 800", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779402319, "dur": 44, "ph": "X", "name": "ReadAsync 800", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779402368, "dur": 3, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779402372, "dur": 39, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779402417, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779402420, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779402459, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779402461, "dur": 32, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779402497, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779402499, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779402536, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779402540, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779402578, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779402581, "dur": 32, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779402615, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779402618, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779402655, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779402658, "dur": 39, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779402701, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779402703, "dur": 35, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779402741, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779402743, "dur": 31, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779402778, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779402781, "dur": 37, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779402821, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779402823, "dur": 35, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779402862, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779402865, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779402901, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779402904, "dur": 46, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779402955, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779402994, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779402997, "dur": 3751, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779406753, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779406755, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779406805, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779406808, "dur": 973, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779407786, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779407788, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779407828, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779407831, "dur": 39, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779407873, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779407875, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779407908, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779407911, "dur": 119, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779408033, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779408037, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779408071, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779408073, "dur": 3995, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779412082, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779412086, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779412133, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779412135, "dur": 29, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779412168, "dur": 93, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779412264, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779412303, "dur": 316, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779412624, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779412666, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779412668, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779412702, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779412705, "dur": 213, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779412921, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779412923, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779412975, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779412978, "dur": 38, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779413018, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779413021, "dur": 126, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779413155, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779413188, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779413190, "dur": 306, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779413499, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779413501, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779413535, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779413537, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779413570, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779413572, "dur": 37, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779413612, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779413615, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779413653, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779413678, "dur": 94, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779413775, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779413777, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779413811, "dur": 65, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779413881, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779413915, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779413917, "dur": 69, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779413991, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779414036, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779414064, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779414066, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779414097, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779414100, "dur": 226, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779414329, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779414331, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779414373, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779414375, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779414428, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779414460, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779414463, "dur": 139, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779414605, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779414640, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779414642, "dur": 55, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779414701, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779414737, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779414741, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779414771, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779414773, "dur": 75, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779414852, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779414854, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779414899, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779414936, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779414973, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779415010, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779415015, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779415051, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779415055, "dur": 30, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779415089, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779415091, "dur": 64, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779415159, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779415191, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779415219, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779415222, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779415247, "dur": 188, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779415439, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779415472, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779415475, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779415511, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779415515, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779415550, "dur": 206, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779415761, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779415792, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779415796, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779415827, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779415829, "dur": 29, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779415863, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779415866, "dur": 29, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779415897, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779415899, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779415935, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779415937, "dur": 31, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779415971, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779415973, "dur": 50, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779416029, "dur": 2, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779416032, "dur": 42, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779416077, "dur": 2, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779416081, "dur": 42, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779416126, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779416128, "dur": 52, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779416183, "dur": 5, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779416189, "dur": 37, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779416229, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779416230, "dur": 34, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779416269, "dur": 295, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779416568, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779416570, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779416609, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779416614, "dur": 39, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779416658, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779416660, "dur": 92, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779416755, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779416757, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779416798, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779416801, "dur": 811, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779417617, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779417664, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779417666, "dur": 41, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779417711, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779417714, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779417762, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779417765, "dur": 37, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779417805, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779417808, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779417848, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779417850, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779417901, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779417903, "dur": 244, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779418151, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779418196, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779418197, "dur": 36, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779418236, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779418238, "dur": 240, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779418482, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779418484, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779418532, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779418535, "dur": 178, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779418719, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779418777, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779418779, "dur": 4000, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779422783, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779422786, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779422834, "dur": 2, "ph": "X", "name": "ProcessMessages 276", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779422838, "dur": 59276, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779482120, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779482124, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779482175, "dur": 305, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751899779482483, "dur": 7203, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 22320, "tid": 555, "ts": 1751899782709510, "dur": 763, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 22320, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 22320, "tid": 8589934592, "ts": 1751899779366107, "dur": 112433, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 22320, "tid": 8589934592, "ts": 1751899779478543, "dur": 7, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 22320, "tid": 8589934592, "ts": 1751899779478551, "dur": 1751, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 22320, "tid": 555, "ts": 1751899782710275, "dur": 5, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 22320, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 22320, "tid": 4294967296, "ts": 1751899779319064, "dur": 172559, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 22320, "tid": 4294967296, "ts": 1751899779346419, "dur": 10369, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 22320, "tid": 4294967296, "ts": 1751899779491834, "dur": 883436, "ph": "X", "name": "await ExecuteBuildProgram", "args": {}}, {"pid": 22320, "tid": 4294967296, "ts": 1751899780378716, "dur": 2312966, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 22320, "tid": 4294967296, "ts": 1751899780378883, "dur": 3645, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 22320, "tid": 4294967296, "ts": 1751899782692142, "dur": 6484, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 22320, "tid": 4294967296, "ts": 1751899782696470, "dur": 76, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 22320, "tid": 4294967296, "ts": 1751899782698632, "dur": 16, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 22320, "tid": 555, "ts": 1751899782710282, "dur": 12, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1751899780408228, "dur": 58, "ph": "X", "name": "IPC_Client_InitializeAndConnectToParent", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751899780408406, "dur": 65670, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751899780474083, "dur": 240, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751899780474393, "dur": 63, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1751899780474457, "dur": 457, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751899780475088, "dur": 111, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_E43C745D5056CF76.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751899780475249, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_F04876DA9D7398DA.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751899780475517, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_31DB0EF841A9843D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751899780475600, "dur": 199, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_2C2D2F6DBE48DE8F.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751899780475830, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_7D12CF9E98D9D784.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751899780476517, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_0197621F1ECC6D18.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751899780477530, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_3520FC5D8B4827F4.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751899780479165, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_EA250D0B58FB5B36.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751899780479687, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_3AA3364A40DB9F8B.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751899780481106, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751899780483757, "dur": 145, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751899780483937, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751899780484184, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751899780484254, "dur": 312, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751899780484573, "dur": 76, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751899780484689, "dur": 154, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751899780484853, "dur": 90, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751899780485192, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751899780485441, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751899780485576, "dur": 79, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751899780490529, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751899780474950, "dur": 16391, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751899780491354, "dur": 2193447, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751899782684985, "dur": 71, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751899782685085, "dur": 1778, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1751899780475401, "dur": 15968, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899780492830, "dur": 330, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "E:/Unity/Editior/6000.0.34f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 1, "ts": 1751899780491376, "dur": 1786, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899780493256, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899780493355, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751899780493463, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899780493540, "dur": 228, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899780493992, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751899780494548, "dur": 245, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899780494794, "dur": 399, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751899780495194, "dur": 985, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899780496179, "dur": 529, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899780496709, "dur": 565, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899780497570, "dur": 916, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.State\\Transitions\\FlowStateTransitionDescriptor.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751899780497280, "dur": 1207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899780498520, "dur": 387, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899780498907, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899780499174, "dur": 503, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Flow\\Framework\\Events\\GUI\\OnSelect.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751899780499142, "dur": 801, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899780499944, "dur": 854, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Core\\Windows\\WebView.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751899780499943, "dur": 1088, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899780501031, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899780501257, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899780501478, "dur": 799, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899780502587, "dur": 1594, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Listeners\\UIInterfaces\\UnityOnPointerDownMessageListener.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751899780502278, "dur": 1935, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899780504213, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899780504406, "dur": 327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899780504733, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899780504953, "dur": 284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899780505237, "dur": 411, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899780505683, "dur": 409, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899780506092, "dur": 592, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899780506728, "dur": 281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751899780507065, "dur": 647, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751899780507713, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899780507857, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751899780508063, "dur": 343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751899780508430, "dur": 657, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751899780509147, "dur": 270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751899780509442, "dur": 1424, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751899780510867, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899780510944, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751899780511043, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899780511117, "dur": 271, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualStudio.Editor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751899780511434, "dur": 229, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899780511697, "dur": 945, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899780512699, "dur": 226, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899780512925, "dur": 593, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899780513518, "dur": 237, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899780513795, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751899780513944, "dur": 315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751899780514342, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899780514399, "dur": 706, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899780515106, "dur": 2169691, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899780475439, "dur": 15943, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899780491398, "dur": 346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_2784FA9BFF134207.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751899780491745, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899780491935, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_12136BD48011B700.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751899780492002, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899780492079, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_8C103AE0E7B61272.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751899780492228, "dur": 164, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_DB68F65C9F3573CD.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751899780492423, "dur": 185, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_EABB3BB0B4DAF932.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751899780492610, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_1D503C7A8290C7BE.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751899780492736, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_BD82F057A3253721.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751899780493835, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1751899780494007, "dur": 427, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751899780494500, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751899780494618, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899780494869, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899780495141, "dur": 443, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899780495585, "dur": 341, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899780495926, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899780496200, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899780496456, "dur": 326, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899780496782, "dur": 442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899780497341, "dur": 1070, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.multiplayer.center\\Editor\\MultiplayerCenterWindow\\MultiplayerCenterWindow.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751899780497224, "dur": 1455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899780498680, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899780498933, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899780499182, "dur": 615, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899780499798, "dur": 285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899780500084, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899780500336, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899780500676, "dur": 764, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Core\\Plugin\\Changelogs\\LegacyLudiqCore\\Changelog_1_4_13.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751899780500562, "dur": 1109, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899780501671, "dur": 780, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899780502452, "dur": 332, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899780502785, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899780503044, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899780503265, "dur": 308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899780503573, "dur": 363, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899780503937, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899780504172, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899780504392, "dur": 304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899780504696, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899780504911, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899780505177, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899780505403, "dur": 355, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899780505758, "dur": 322, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899780506080, "dur": 597, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899780506706, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751899780506925, "dur": 544, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751899780507533, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899780507605, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751899780507771, "dur": 2378, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751899780510193, "dur": 205, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751899780510437, "dur": 190, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.PlasticSCM.Editor.ref.dll_257AEB342BE77856.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751899780510698, "dur": 252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751899780510969, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751899780511031, "dur": 570, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751899780511674, "dur": 1019, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899780512693, "dur": 235, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899780512929, "dur": 584, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899780513513, "dur": 240, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899780513786, "dur": 558, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899780514344, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899780514406, "dur": 708, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899780515114, "dur": 2169460, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899780475501, "dur": 15899, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899780491404, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_7359E91D15751A95.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751899780491556, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_7D12CF9E98D9D784.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751899780491732, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_0D411EBC79DB1BBD.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751899780491806, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_86082FB9E11717DE.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751899780492141, "dur": 113, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_42529D1BD0B2CBE0.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751899780492377, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_9D5FC13BBE3E7A4F.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751899780492483, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_7B05F0D16B4953B8.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751899780492550, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899780493272, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751899780493575, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899780493998, "dur": 122, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751899780494281, "dur": 139, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751899780494530, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899780494602, "dur": 381, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899780494983, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899780495198, "dur": 435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899780495634, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899780495900, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899780496094, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899780496348, "dur": 776, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899780497125, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899780497338, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899780497598, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899780497864, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899780498120, "dur": 500, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Flow\\Framework\\Variables\\SetVariableOption.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751899780498105, "dur": 739, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899780498845, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899780499118, "dur": 308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899780499427, "dur": 345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899780499773, "dur": 881, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899780500654, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899780500923, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899780501159, "dur": 344, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899780501766, "dur": 602, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Reflection\\Optimization\\OptimizedReflection.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751899780501503, "dur": 1335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899780502838, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899780503080, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899780503298, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899780503512, "dur": 380, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899780503892, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899780504112, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899780504327, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899780504576, "dur": 685, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899780505262, "dur": 345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899780505683, "dur": 395, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899780506118, "dur": 550, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899780506721, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751899780506941, "dur": 625, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751899780507567, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899780507693, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751899780507773, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899780507898, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751899780508116, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751899780508361, "dur": 766, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751899780509128, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899780509207, "dur": 1334, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751899780510577, "dur": 255, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751899780511030, "dur": 115, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PlasticSCM.Editor.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1751899780511149, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899780511373, "dur": 67, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899780511440, "dur": 1260, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899780512700, "dur": 221, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899780512952, "dur": 530, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899780513486, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899780513543, "dur": 246, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899780513789, "dur": 521, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899780514339, "dur": 69, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899780514408, "dur": 702, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899780515111, "dur": 2169465, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751899780475622, "dur": 15786, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751899780491414, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_0E44AF698B21F99B.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751899780491618, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751899780491823, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_0197621F1ECC6D18.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751899780491890, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751899780491985, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_B7F547A15B2C01B2.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751899780492371, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751899780492471, "dur": 149, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_92A5F53C232CE1D9.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751899780492622, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_B9448EEF9FF8FB2A.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751899780492833, "dur": 212, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_B9448EEF9FF8FB2A.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751899780493284, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751899780493460, "dur": 7643, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751899780501212, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751899780501353, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751899780501539, "dur": 4471, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751899780506107, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751899780506237, "dur": 373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751899780506706, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751899780506918, "dur": 1581, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751899780508500, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751899780508631, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751899780508769, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751899780508949, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751899780509186, "dur": 2162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751899780511431, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751899780511618, "dur": 1246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751899780512948, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751899780513086, "dur": 345, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751899780513503, "dur": 257, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751899780513760, "dur": 592, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751899780514400, "dur": 689, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751899780515128, "dur": 2169336, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899780475748, "dur": 15678, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899780491431, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_AD3F2B6EFF130063.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751899780491493, "dur": 322, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899780491907, "dur": 356, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_497F9890AE502636.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751899780492265, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_656AB5FDB0417D9D.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751899780492535, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_5BB3CA76865503EA.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751899780492784, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_BF2CDC1257CA045F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751899780492967, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_B1D6EB5640F05713.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751899780493101, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_B1D6EB5640F05713.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751899780494013, "dur": 293, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751899780494307, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751899780494385, "dur": 230, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899780494752, "dur": 785, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.visualstudio\\Editor\\Messaging\\Message.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751899780495537, "dur": 852, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.visualstudio\\Editor\\Messaging\\ExceptionEventArgs.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751899780494623, "dur": 1885, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899780496539, "dur": 1085, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.testtools.codecoverage\\Editor\\Analytics\\CoverageAnalyticsEnums.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751899780496508, "dur": 1430, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899780497939, "dur": 734, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899780498674, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899780498911, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899780499182, "dur": 809, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Flow\\Framework\\Events\\Input\\OnMouseUpAsButton.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751899780499135, "dur": 1181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899780500316, "dur": 357, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899780500674, "dur": 311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899780500985, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899780501248, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899780501515, "dur": 1055, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899780502679, "dur": 663, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Exceptions\\DebugUtility.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751899780502570, "dur": 1101, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899780503672, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899780503899, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899780504113, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899780504355, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899780504577, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899780504799, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899780505015, "dur": 796, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899780505811, "dur": 282, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899780506093, "dur": 586, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899780506679, "dur": 1131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899780507811, "dur": 752, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751899780508611, "dur": 628, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751899780509286, "dur": 841, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751899780510184, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751899780510374, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899780510480, "dur": 288, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751899780510772, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899780511062, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899780511439, "dur": 1212, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899780512681, "dur": 257, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899780512938, "dur": 580, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899780513518, "dur": 234, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899780513782, "dur": 584, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899780514403, "dur": 608, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899780515109, "dur": 2169642, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751899780475702, "dur": 15715, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751899780491423, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_AAC1DDBB583EF4F7.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751899780491569, "dur": 125, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_AAC1DDBB583EF4F7.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751899780491911, "dur": 267, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_2504973AF8D3F8E7.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751899780492241, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_0C31EB717FD7AB62.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751899780492469, "dur": 167, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_D8B62AD188B30A07.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751899780492638, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_7882E8644625FF23.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751899780493287, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751899780493450, "dur": 4974, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751899780498501, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751899780498744, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751899780498970, "dur": 899, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751899780499870, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751899780500124, "dur": 658, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751899780501259, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751899780501770, "dur": 586, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Reflection\\Optimization\\StaticActionInvoker_5.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751899780501486, "dur": 934, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751899780502421, "dur": 278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751899780502700, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751899780502949, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751899780503172, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751899780503389, "dur": 292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751899780503682, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751899780503897, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751899780504118, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751899780504336, "dur": 286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751899780504622, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751899780504845, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751899780505134, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751899780505374, "dur": 380, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751899780505755, "dur": 326, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751899780506082, "dur": 590, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751899780506716, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751899780506936, "dur": 554, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751899780507490, "dur": 351, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751899780507879, "dur": 315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751899780508195, "dur": 364, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751899780508569, "dur": 676, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751899780509246, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751899780509409, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751899780509628, "dur": 601, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751899780510230, "dur": 183, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751899780510415, "dur": 509, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751899780510925, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751899780511249, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rider.Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751899780511347, "dur": 97, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751899780511445, "dur": 1256, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751899780512701, "dur": 222, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751899780512953, "dur": 528, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751899780513513, "dur": 259, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751899780513772, "dur": 575, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751899780514347, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751899780514428, "dur": 689, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751899780515117, "dur": 2169415, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899780475785, "dur": 15667, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899780491493, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899780491717, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_111B28F0FC0CD3DB.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751899780491788, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899780492555, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_40A52831373DC9B0.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751899780492789, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_D380F5A76DBED59A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751899780492892, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_83F4E4F0FA017AFD.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751899780493950, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1751899780494108, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751899780494278, "dur": 119, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751899780495762, "dur": 561, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751899780496379, "dur": 586, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui\\Editor\\TMP\\TMP_EditorPanel.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751899780496325, "dur": 799, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899780497125, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899780497367, "dur": 376, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899780497743, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899780497982, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899780498197, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899780498605, "dur": 1272, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Flow\\Connections\\UnitConnectionStyles.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751899780498508, "dur": 1529, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899780500038, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899780500272, "dur": 303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899780500575, "dur": 293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899780500869, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899780501093, "dur": 384, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899780501477, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899780501718, "dur": 712, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899780502430, "dur": 316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899780502746, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899780502994, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899780503217, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899780503432, "dur": 322, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899780503754, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899780503974, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899780504195, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899780504406, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899780504638, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899780504857, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899780505115, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899780505292, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899780505618, "dur": 57, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899780505686, "dur": 391, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899780506108, "dur": 562, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899780506715, "dur": 294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751899780507039, "dur": 792, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751899780507832, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899780508003, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751899780508269, "dur": 2079, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751899780510349, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899780510485, "dur": 338, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TestTools.CodeCoverage.Editor.ref.dll_F09CF4826CE11CD9.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751899780511077, "dur": 123, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PlasticSCM.Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751899780511375, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899780511433, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751899780511607, "dur": 977, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751899780512691, "dur": 242, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899780512933, "dur": 575, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899780513508, "dur": 279, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899780513788, "dur": 520, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899780514420, "dur": 683, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899780515103, "dur": 2149593, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899782664720, "dur": 333, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751899782664697, "dur": 357, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751899782665102, "dur": 2108, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751899782667216, "dur": 17379, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751899780475837, "dur": 15623, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751899780491496, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751899780491631, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_78F0F79A0FB195D1.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751899780491711, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_78F0F79A0FB195D1.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751899780491778, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_55EDFAE7741AF365.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751899780491832, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751899780492124, "dur": 154, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_D80D577A1177CFEE.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751899780492413, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_82A68248955BDDC4.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751899780492492, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751899780492676, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_7FF6DD7231CA2BE3.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751899780493258, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751899780493372, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751899780493843, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751899780493947, "dur": 809, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1751899780494757, "dur": 336, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751899780495094, "dur": 426, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751899780495641, "dur": 622, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\Signals\\SignalEmitterInspector.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751899780495520, "dur": 870, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751899780496538, "dur": 1078, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.testtools.codecoverage\\Editor\\Events\\CoverageEventData.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751899780496390, "dur": 1517, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751899780497907, "dur": 294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751899780498201, "dur": 588, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751899780498790, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751899780499053, "dur": 349, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751899780499402, "dur": 630, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751899780500032, "dur": 404, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751899780500436, "dur": 315, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751899780500751, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751899780501008, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751899780501495, "dur": 726, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Core\\Analytics\\OnPreprocessBuildAnalyticsEventHandler.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751899780501256, "dur": 1067, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751899780502324, "dur": 325, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751899780502650, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751899780502892, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751899780503098, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751899780503304, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751899780503583, "dur": 1124, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\Plugins\\WebGL\\WebGLSupport.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751899780503508, "dur": 1332, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751899780504840, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751899780505053, "dur": 170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751899780505224, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751899780505622, "dur": 73, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751899780505695, "dur": 392, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751899780506087, "dur": 629, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751899780506719, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751899780506921, "dur": 610, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751899780507531, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751899780507745, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.ref.dll_84A4293DBDD182DB.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751899780507802, "dur": 524, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751899780508346, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751899780508415, "dur": 850, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751899780509335, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751899780509396, "dur": 238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751899780509635, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.EditorCoroutines.Editor.ref.dll_34ED10A34098B2DB.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751899780509729, "dur": 291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751899780510080, "dur": 845, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751899780510926, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751899780511073, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.DocCodeSamples.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1751899780511188, "dur": 210, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Performance.Profile-Analyzer.Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751899780511434, "dur": 1261, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751899780512695, "dur": 227, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751899780512950, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751899780513088, "dur": 607, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751899780513794, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751899780513946, "dur": 391, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751899780514417, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751899780514542, "dur": 395, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751899780515127, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751899780515765, "dur": 1120, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751899780515283, "dur": 1604, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751899780517195, "dur": 71, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751899780518628, "dur": 2144275, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751899782664697, "dur": 18532, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751899782664683, "dur": 18548, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751899782683257, "dur": 1166, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751899782690802, "dur": 1254, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 35942, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "netcorerun.dll"}}, {"pid": 35942, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-1"}}, {"pid": 35942, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 35942, "tid": 1, "ts": 1751899779832770, "dur": 517461, "ph": "X", "name": "BuildProgram", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1751899779834414, "dur": 177900, "ph": "X", "name": "BuildProgramContextConstructor", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1751899780312792, "dur": 4701, "ph": "X", "name": "OutputData.Write", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1751899780317497, "dur": 32721, "ph": "X", "name": "Backend.Write", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1751899780318788, "dur": 25540, "ph": "X", "name": "JsonToString", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1751899780361798, "dur": 1475, "ph": "X", "name": "", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1751899780360873, "dur": 2659, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1751899779380602, "dur": 2684, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751899779383302, "dur": 455, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751899779383813, "dur": 50, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1751899779383863, "dur": 395, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751899779385705, "dur": 2575, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_3BACF753C5B41AC5.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751899779389182, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_B44D12B45B4CC90C.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751899779389725, "dur": 2351, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751899779392151, "dur": 112, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751899779384283, "dur": 16972, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751899779401266, "dur": 81768, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751899779483072, "dur": 51, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751899779483189, "dur": 83, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751899779483297, "dur": 1702, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1751899779384902, "dur": 16428, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899779401335, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_AD3F2B6EFF130063.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751899779401588, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_4DE8BB45CF6CF9CC.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751899779401978, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899779402235, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_D380F5A76DBED59A.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751899779402707, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751899779402777, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899779403534, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751899779403589, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899779403675, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899779403741, "dur": 100, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751899779403872, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751899779403963, "dur": 798, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899779404762, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899779405001, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899779405220, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899779405429, "dur": 472, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899779405901, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899779406118, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899779406338, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899779406549, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899779406790, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899779407011, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899779407273, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899779407513, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899779407729, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899779407989, "dur": 321, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899779408310, "dur": 624, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899779408934, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899779409168, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899779409406, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899779409619, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899779409814, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899779410021, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899779410254, "dur": 305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899779410560, "dur": 486, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899779411046, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899779411280, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899779411509, "dur": 546, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899779412056, "dur": 108, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899779412197, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899779412394, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899779412622, "dur": 61, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899779412819, "dur": 499, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899779413318, "dur": 533, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899779413877, "dur": 78, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899779413957, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751899779414152, "dur": 1615, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751899779415821, "dur": 1184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751899779417204, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899779417291, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.EditorCoroutines.Editor.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1751899779417364, "dur": 50, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899779417438, "dur": 363, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899779417822, "dur": 1022, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899779418844, "dur": 94, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899779418939, "dur": 434, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899779419374, "dur": 294, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899779419668, "dur": 714, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899779420425, "dur": 428, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751899779420877, "dur": 62071, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899779384630, "dur": 16650, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899779401293, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_2784FA9BFF134207.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751899779401578, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_F9300E41473E6FF2.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751899779401640, "dur": 205, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_F9300E41473E6FF2.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751899779401976, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899779402837, "dur": 287, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899779403128, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751899779403299, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899779403439, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751899779403564, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899779403827, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1751899779403899, "dur": 259, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899779404197, "dur": 495, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899779404693, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899779404945, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899779405149, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899779405362, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899779405581, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899779405824, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899779406047, "dur": 695, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899779406743, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899779406963, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899779407251, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899779407498, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899779407728, "dur": 322, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899779408050, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899779408317, "dur": 625, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899779408942, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899779409194, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899779409437, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899779409649, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899779409858, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899779410063, "dur": 439, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899779410502, "dur": 436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899779410938, "dur": 333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899779411272, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899779411489, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899779412285, "dur": 555, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy\\Editor\\AssetOverlays\\Cache\\LocalStatusCache.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751899779411684, "dur": 1190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899779412875, "dur": 417, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899779413321, "dur": 542, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899779413913, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751899779414084, "dur": 740, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899779414828, "dur": 729, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751899779415558, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899779415669, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751899779415853, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899779415914, "dur": 690, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751899779416604, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899779416757, "dur": 592, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751899779417434, "dur": 375, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899779417809, "dur": 1041, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899779418850, "dur": 82, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899779418964, "dur": 406, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899779419392, "dur": 270, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899779419662, "dur": 742, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899779420404, "dur": 453, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751899779420857, "dur": 62072, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899779384695, "dur": 16600, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899779401300, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_7359E91D15751A95.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751899779401985, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899779402338, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_74C5C7E8DEA943A7.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751899779402418, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899779402545, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899779402736, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1751899779402840, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899779403258, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899779403378, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1751899779403553, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899779403629, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751899779403839, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1751899779403990, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899779404201, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899779404426, "dur": 391, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899779404818, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899779405035, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899779405256, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899779405468, "dur": 521, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899779405990, "dur": 560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899779406550, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899779406791, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899779407016, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899779407275, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899779407529, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899779407769, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899779407999, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899779408242, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899779408448, "dur": 610, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899779409105, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899779409344, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899779409577, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899779409796, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899779409998, "dur": 356, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899779410354, "dur": 318, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899779410672, "dur": 420, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899779411093, "dur": 680, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899779411773, "dur": 509, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899779412283, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899779412525, "dur": 360, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899779412885, "dur": 422, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899779413308, "dur": 552, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899779413861, "dur": 70, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899779413933, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751899779414159, "dur": 568, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751899779414794, "dur": 272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751899779415114, "dur": 954, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751899779416239, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751899779416432, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751899779416653, "dur": 476, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751899779417283, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Settings.Editor.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1751899779417351, "dur": 80, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899779417431, "dur": 397, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899779417828, "dur": 1011, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899779418869, "dur": 61, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899779418952, "dur": 419, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899779419409, "dur": 257, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899779419667, "dur": 710, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899779420400, "dur": 456, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899779420856, "dur": 60666, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751899779481523, "dur": 1355, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751899779384788, "dur": 16518, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751899779401312, "dur": 420, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_0E44AF698B21F99B.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751899779401973, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_1D34F993682102CF.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751899779402099, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_7882E8644625FF23.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751899779402324, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_26531C2D0F795B12.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751899779402629, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751899779402771, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751899779402946, "dur": 4963, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751899779407999, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751899779408261, "dur": 589, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751899779408851, "dur": 488, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751899779409521, "dur": 541, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\fsDirectConverter.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751899779409339, "dur": 770, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751899779410110, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751899779410359, "dur": 298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751899779410657, "dur": 448, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751899779411106, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751899779411351, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751899779411572, "dur": 735, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751899779412308, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751899779412547, "dur": 388, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751899779412936, "dur": 392, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751899779413328, "dur": 534, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751899779413862, "dur": 52, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751899779413915, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751899779414133, "dur": 529, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751899779414742, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751899779414990, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751899779415188, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751899779415280, "dur": 721, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751899779416049, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751899779416117, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751899779416374, "dur": 492, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751899779416867, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751899779417296, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751899779417444, "dur": 354, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751899779417827, "dur": 1014, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751899779418841, "dur": 99, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751899779418940, "dur": 436, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751899779419376, "dur": 287, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751899779419663, "dur": 723, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751899779420434, "dur": 427, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751899779420861, "dur": 62128, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899779384886, "dur": 16432, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899779401323, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_AAC1DDBB583EF4F7.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751899779401469, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899779402030, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_5BB3CA76865503EA.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751899779402143, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899779402341, "dur": 274, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_5911DF3198B3A870.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751899779402647, "dur": 242, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899779403118, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899779403361, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1751899779403518, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899779403603, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751899779403681, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899779403758, "dur": 109, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751899779403912, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751899779404005, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899779404286, "dur": 402, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899779404688, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899779404918, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899779405130, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899779405351, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899779405575, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899779405816, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899779406048, "dur": 513, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899779406561, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899779406808, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899779407033, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899779407290, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899779407548, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899779407782, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899779408004, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899779408256, "dur": 594, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899779408871, "dur": 1342, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Reflection\\Optimization\\InstanceActionInvoker_1.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751899779408850, "dur": 1578, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899779410428, "dur": 915, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899779411344, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899779411562, "dur": 650, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899779412213, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899779412457, "dur": 375, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899779412832, "dur": 462, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899779413295, "dur": 573, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899779413868, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899779413929, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751899779414146, "dur": 631, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751899779414777, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899779414866, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751899779415091, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751899779415290, "dur": 742, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751899779416033, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899779416143, "dur": 915, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751899779417059, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899779417337, "dur": 65, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899779417424, "dur": 376, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899779417821, "dur": 1025, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899779418846, "dur": 87, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899779418933, "dur": 445, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899779419378, "dur": 277, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899779419681, "dur": 697, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899779420405, "dur": 450, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899779420856, "dur": 454, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751899779421337, "dur": 61713, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751899779384982, "dur": 16364, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751899779401352, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_31DB0EF841A9843D.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751899779401587, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751899779401975, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_4E97288DD1B11317.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751899779402603, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751899779402697, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751899779403364, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1751899779403523, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751899779403884, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751899779404069, "dur": 387, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751899779404456, "dur": 397, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751899779404854, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751899779405067, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751899779405291, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751899779405565, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751899779405821, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751899779406039, "dur": 610, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751899779406649, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751899779406872, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751899779407128, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751899779407367, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751899779407606, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751899779407848, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751899779408080, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751899779408310, "dur": 611, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751899779408921, "dur": 317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751899779409238, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751899779409510, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751899779409743, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751899779409982, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751899779410225, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751899779410442, "dur": 428, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751899779410870, "dur": 425, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751899779411296, "dur": 278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751899779411575, "dur": 799, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751899779412374, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751899779412604, "dur": 310, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751899779412914, "dur": 383, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751899779413297, "dur": 561, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751899779413927, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751899779414139, "dur": 560, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751899779414770, "dur": 447, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751899779415256, "dur": 866, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751899779416122, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751899779416237, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751899779416459, "dur": 468, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751899779417036, "dur": 185, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751899779417225, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751899779417283, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PlasticSCM.Editor.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1751899779417354, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751899779417445, "dur": 373, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751899779417822, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751899779417982, "dur": 902, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751899779418956, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751899779419088, "dur": 511, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751899779419674, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751899779419846, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751899779419949, "dur": 397, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751899779420418, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751899779420555, "dur": 252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751899779420875, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751899779420999, "dur": 278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751899779421319, "dur": 61761, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899779385083, "dur": 16276, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899779401362, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_2C2D2F6DBE48DE8F.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751899779401583, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899779401829, "dur": 495, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899779402983, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899779403211, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899779403381, "dur": 176, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1751899779404013, "dur": 332, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899779404345, "dur": 391, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899779404736, "dur": 306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899779405043, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899779405263, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899779405479, "dur": 289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899779405768, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899779407119, "dur": 1780, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Flow\\Framework\\Variables\\SetVariable.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751899779405990, "dur": 2972, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899779408962, "dur": 364, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899779409326, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899779409543, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899779409808, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899779410007, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899779410229, "dur": 347, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899779410576, "dur": 688, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899779411265, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899779411489, "dur": 473, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899779411962, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899779412204, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899779412440, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899779412819, "dur": 474, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899779413338, "dur": 528, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899779413866, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899779413925, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751899779414129, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751899779414218, "dur": 848, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751899779415067, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899779415194, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751899779415299, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751899779415474, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899779415539, "dur": 1722, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751899779417297, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751899779417449, "dur": 389, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899779417838, "dur": 999, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899779418861, "dur": 73, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899779418934, "dur": 447, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899779419381, "dur": 276, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899779419683, "dur": 692, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899779420406, "dur": 448, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751899779420880, "dur": 62037, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751899779385192, "dur": 16191, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751899779401384, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_BD8ABEB80D593DCC.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751899779401573, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_FC3AA1C0027C0ADA.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751899779401723, "dur": 310, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_BCD4830FA0E11DD6.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751899779402042, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_40A52831373DC9B0.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751899779402159, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751899779402341, "dur": 407, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_40A52831373DC9B0.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751899779402770, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751899779403005, "dur": 5944, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751899779409024, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751899779409232, "dur": 4001, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751899779413335, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751899779413480, "dur": 317, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751899779414111, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751899779414393, "dur": 1516, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751899779415973, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751899779416201, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751899779416279, "dur": 1462, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751899779417813, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751899779417962, "dur": 824, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751899779418859, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751899779419017, "dur": 312, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751899779419390, "dur": 261, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751899779419675, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751899779419901, "dur": 411, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751899779420399, "dur": 461, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751899779420860, "dur": 62154, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751899779489158, "dur": 1141, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 22320, "tid": 555, "ts": 1751899782710844, "dur": 2338, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend2.traceevents"}}, {"pid": 22320, "tid": 555, "ts": 1751899782715492, "dur": 45, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "buildprogram0.traceevents"}}, {"pid": 22320, "tid": 555, "ts": 1751899782715751, "dur": 18, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 22320, "tid": 555, "ts": 1751899782713231, "dur": 2254, "ph": "X", "name": "backend2.traceevents", "args": {}}, {"pid": 22320, "tid": 555, "ts": 1751899782715570, "dur": 180, "ph": "X", "name": "buildprogram0.traceevents", "args": {}}, {"pid": 22320, "tid": 555, "ts": 1751899782715788, "dur": 467, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 22320, "tid": 555, "ts": 1751899782705284, "dur": 11905, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}