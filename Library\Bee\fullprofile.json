{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 20696, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 20696, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 20696, "tid": 18, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 20696, "tid": 18, "ts": 1751885346424635, "dur": 945, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 20696, "tid": 18, "ts": 1751885346432195, "dur": 1258, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 20696, "tid": 21474836480, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330473495, "dur": 19501, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330492997, "dur": 15917435, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330493009, "dur": 35, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330493051, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330493053, "dur": 46126, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330539191, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330539196, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330539254, "dur": 7, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330539263, "dur": 2785, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330542055, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330542057, "dur": 73, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330542136, "dur": 3, "ph": "X", "name": "ProcessMessages 1022", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330542140, "dur": 95, "ph": "X", "name": "ReadAsync 1022", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330542240, "dur": 2, "ph": "X", "name": "ProcessMessages 646", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330542244, "dur": 72, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330542319, "dur": 2, "ph": "X", "name": "ProcessMessages 1184", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330542323, "dur": 63, "ph": "X", "name": "ReadAsync 1184", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330542390, "dur": 2, "ph": "X", "name": "ProcessMessages 687", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330542394, "dur": 66, "ph": "X", "name": "ReadAsync 687", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330542464, "dur": 2, "ph": "X", "name": "ProcessMessages 537", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330542468, "dur": 47, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330542518, "dur": 2, "ph": "X", "name": "ProcessMessages 546", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330542521, "dur": 90, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330542615, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330542617, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330542675, "dur": 1, "ph": "X", "name": "ProcessMessages 719", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330542678, "dur": 63, "ph": "X", "name": "ReadAsync 719", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330542743, "dur": 1, "ph": "X", "name": "ProcessMessages 417", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330542746, "dur": 59, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330542809, "dur": 1, "ph": "X", "name": "ProcessMessages 384", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330542812, "dur": 59, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330542875, "dur": 1, "ph": "X", "name": "ProcessMessages 598", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330542878, "dur": 54, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330542934, "dur": 1, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330542935, "dur": 50, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330542988, "dur": 1, "ph": "X", "name": "ProcessMessages 593", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330542991, "dur": 50, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330543044, "dur": 1, "ph": "X", "name": "ProcessMessages 488", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330543047, "dur": 54, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330543104, "dur": 1, "ph": "X", "name": "ProcessMessages 739", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330543107, "dur": 82, "ph": "X", "name": "ReadAsync 739", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330543193, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330543196, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330543257, "dur": 2, "ph": "X", "name": "ProcessMessages 640", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330543260, "dur": 53, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330543316, "dur": 2, "ph": "X", "name": "ProcessMessages 722", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330543320, "dur": 39, "ph": "X", "name": "ReadAsync 722", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330543366, "dur": 54, "ph": "X", "name": "ReadAsync 5", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330543423, "dur": 1, "ph": "X", "name": "ProcessMessages 714", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330543426, "dur": 85, "ph": "X", "name": "ReadAsync 714", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330543515, "dur": 1, "ph": "X", "name": "ProcessMessages 1", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330543518, "dur": 63, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330543584, "dur": 2, "ph": "X", "name": "ProcessMessages 1203", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330543587, "dur": 43, "ph": "X", "name": "ReadAsync 1203", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330543633, "dur": 1, "ph": "X", "name": "ProcessMessages 53", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330543635, "dur": 55, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330543692, "dur": 1, "ph": "X", "name": "ProcessMessages 801", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330543695, "dur": 98, "ph": "X", "name": "ReadAsync 801", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330543798, "dur": 63, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330543864, "dur": 2, "ph": "X", "name": "ProcessMessages 960", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330543867, "dur": 46, "ph": "X", "name": "ReadAsync 960", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330543917, "dur": 1, "ph": "X", "name": "ProcessMessages 115", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330543920, "dur": 164, "ph": "X", "name": "ReadAsync 115", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330544088, "dur": 2, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330544091, "dur": 71, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330544166, "dur": 3, "ph": "X", "name": "ProcessMessages 1581", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330544170, "dur": 61, "ph": "X", "name": "ReadAsync 1581", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330544234, "dur": 1, "ph": "X", "name": "ProcessMessages 593", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330544237, "dur": 52, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330544292, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330544294, "dur": 52, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330544351, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330544414, "dur": 1, "ph": "X", "name": "ProcessMessages 594", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330544416, "dur": 54, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330544474, "dur": 1, "ph": "X", "name": "ProcessMessages 197", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330544476, "dur": 55, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330544534, "dur": 1, "ph": "X", "name": "ProcessMessages 635", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330544537, "dur": 53, "ph": "X", "name": "ReadAsync 635", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330544595, "dur": 53, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330544650, "dur": 1, "ph": "X", "name": "ProcessMessages 598", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330544653, "dur": 96, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330544755, "dur": 47, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330544805, "dur": 1, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330544808, "dur": 51, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330544862, "dur": 1, "ph": "X", "name": "ProcessMessages 842", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330544864, "dur": 99, "ph": "X", "name": "ReadAsync 842", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330544968, "dur": 63, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330545036, "dur": 2, "ph": "X", "name": "ProcessMessages 1296", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330545040, "dur": 116, "ph": "X", "name": "ReadAsync 1296", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330545160, "dur": 1, "ph": "X", "name": "ProcessMessages 696", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330545162, "dur": 55, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330545220, "dur": 1, "ph": "X", "name": "ProcessMessages 919", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330545222, "dur": 48, "ph": "X", "name": "ReadAsync 919", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330545272, "dur": 1, "ph": "X", "name": "ProcessMessages 628", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330545275, "dur": 46, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330545324, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330545326, "dur": 46, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330545376, "dur": 1, "ph": "X", "name": "ProcessMessages 1", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330545378, "dur": 59, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330545439, "dur": 1, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330545441, "dur": 51, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330545496, "dur": 2, "ph": "X", "name": "ProcessMessages 854", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330545499, "dur": 53, "ph": "X", "name": "ReadAsync 854", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330545556, "dur": 1, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330545558, "dur": 45, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330545608, "dur": 2, "ph": "X", "name": "ProcessMessages 474", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330545611, "dur": 47, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330545662, "dur": 1, "ph": "X", "name": "ProcessMessages 456", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330545664, "dur": 44, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330545711, "dur": 1, "ph": "X", "name": "ProcessMessages 594", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330545714, "dur": 56, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330545776, "dur": 98, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330545877, "dur": 1, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330545879, "dur": 48, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330545930, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330545933, "dur": 45, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330545983, "dur": 44, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330546029, "dur": 1, "ph": "X", "name": "ProcessMessages 462", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330546032, "dur": 42, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330546077, "dur": 1, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330546079, "dur": 45, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330546128, "dur": 1, "ph": "X", "name": "ProcessMessages 584", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330546130, "dur": 44, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330546177, "dur": 1, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330546180, "dur": 45, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330546227, "dur": 1, "ph": "X", "name": "ProcessMessages 646", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330546230, "dur": 48, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330546282, "dur": 2, "ph": "X", "name": "ProcessMessages 630", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330546285, "dur": 40, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330546329, "dur": 1, "ph": "X", "name": "ProcessMessages 640", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330546331, "dur": 42, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330546376, "dur": 1, "ph": "X", "name": "ProcessMessages 524", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330546378, "dur": 42, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330546423, "dur": 1, "ph": "X", "name": "ProcessMessages 482", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330546426, "dur": 42, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330546471, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330546473, "dur": 37, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330546514, "dur": 1, "ph": "X", "name": "ProcessMessages 248", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330546517, "dur": 38, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330546557, "dur": 1, "ph": "X", "name": "ProcessMessages 284", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330546559, "dur": 53, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330546614, "dur": 1, "ph": "X", "name": "ProcessMessages 620", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330546617, "dur": 51, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330546672, "dur": 1, "ph": "X", "name": "ProcessMessages 584", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330546675, "dur": 66, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330546744, "dur": 1, "ph": "X", "name": "ProcessMessages 748", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330546746, "dur": 44, "ph": "X", "name": "ReadAsync 748", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330546794, "dur": 1, "ph": "X", "name": "ProcessMessages 621", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330546797, "dur": 44, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330546844, "dur": 1, "ph": "X", "name": "ProcessMessages 471", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330546846, "dur": 46, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330546895, "dur": 1, "ph": "X", "name": "ProcessMessages 622", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330546897, "dur": 47, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330546947, "dur": 1, "ph": "X", "name": "ProcessMessages 535", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330546949, "dur": 40, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330546993, "dur": 1, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330546995, "dur": 41, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330547039, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330547083, "dur": 1, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330547086, "dur": 56, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330547146, "dur": 1, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330547149, "dur": 57, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330547210, "dur": 2, "ph": "X", "name": "ProcessMessages 464", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330547213, "dur": 49, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330547265, "dur": 2, "ph": "X", "name": "ProcessMessages 642", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330547269, "dur": 41, "ph": "X", "name": "ReadAsync 642", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330547313, "dur": 1, "ph": "X", "name": "ProcessMessages 400", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330547315, "dur": 35, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330547355, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330547405, "dur": 1, "ph": "X", "name": "ProcessMessages 302", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330547406, "dur": 35, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330547445, "dur": 35, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330547484, "dur": 175, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330547661, "dur": 1, "ph": "X", "name": "ProcessMessages 507", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330547663, "dur": 117, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330547785, "dur": 2, "ph": "X", "name": "ProcessMessages 1614", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330547788, "dur": 58, "ph": "X", "name": "ReadAsync 1614", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330547848, "dur": 1, "ph": "X", "name": "ProcessMessages 1423", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330547850, "dur": 44, "ph": "X", "name": "ReadAsync 1423", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330547897, "dur": 1, "ph": "X", "name": "ProcessMessages 278", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330547899, "dur": 48, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330547950, "dur": 1, "ph": "X", "name": "ProcessMessages 631", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330547952, "dur": 41, "ph": "X", "name": "ReadAsync 631", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330547995, "dur": 1, "ph": "X", "name": "ProcessMessages 391", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330547997, "dur": 53, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330548053, "dur": 1, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330548055, "dur": 37, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330548095, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330548097, "dur": 33, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330548133, "dur": 39, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330548174, "dur": 1, "ph": "X", "name": "ProcessMessages 537", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330548175, "dur": 38, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330548215, "dur": 1, "ph": "X", "name": "ProcessMessages 355", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330548217, "dur": 39, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330548259, "dur": 1, "ph": "X", "name": "ProcessMessages 464", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330548261, "dur": 32, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330548296, "dur": 35, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330548334, "dur": 35, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330548371, "dur": 1, "ph": "X", "name": "ProcessMessages 407", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330548373, "dur": 37, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330548412, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330548414, "dur": 32, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330548450, "dur": 35, "ph": "X", "name": "ReadAsync 214", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330548488, "dur": 34, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330548524, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330548526, "dur": 40, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330548567, "dur": 1, "ph": "X", "name": "ProcessMessages 656", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330548569, "dur": 38, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330548610, "dur": 35, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330548647, "dur": 1, "ph": "X", "name": "ProcessMessages 387", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330548649, "dur": 36, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330548688, "dur": 33, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330548723, "dur": 1, "ph": "X", "name": "ProcessMessages 321", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330548725, "dur": 45, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330548772, "dur": 1, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330548774, "dur": 36, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330548814, "dur": 35, "ph": "X", "name": "ReadAsync 653", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330548852, "dur": 1, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330548853, "dur": 38, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330548894, "dur": 1, "ph": "X", "name": "ProcessMessages 607", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330548896, "dur": 33, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330548933, "dur": 34, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330548968, "dur": 1, "ph": "X", "name": "ProcessMessages 535", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330548970, "dur": 39, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330549012, "dur": 1, "ph": "X", "name": "ProcessMessages 702", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330549014, "dur": 43, "ph": "X", "name": "ReadAsync 702", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330549061, "dur": 1, "ph": "X", "name": "ProcessMessages 640", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330549064, "dur": 41, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330549108, "dur": 1, "ph": "X", "name": "ProcessMessages 595", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330549110, "dur": 47, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330549160, "dur": 1, "ph": "X", "name": "ProcessMessages 572", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330549162, "dur": 42, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330549206, "dur": 1, "ph": "X", "name": "ProcessMessages 321", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330549208, "dur": 47, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330549258, "dur": 1, "ph": "X", "name": "ProcessMessages 601", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330549261, "dur": 42, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330549305, "dur": 1, "ph": "X", "name": "ProcessMessages 419", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330549308, "dur": 46, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330549356, "dur": 1, "ph": "X", "name": "ProcessMessages 692", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330549359, "dur": 49, "ph": "X", "name": "ReadAsync 692", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330549410, "dur": 1, "ph": "X", "name": "ProcessMessages 510", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330549412, "dur": 41, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330549456, "dur": 1, "ph": "X", "name": "ProcessMessages 389", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330549458, "dur": 53, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330549514, "dur": 2, "ph": "X", "name": "ProcessMessages 262", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330549518, "dur": 47, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330549568, "dur": 1, "ph": "X", "name": "ProcessMessages 646", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330549570, "dur": 38, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330549610, "dur": 1, "ph": "X", "name": "ProcessMessages 328", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330549612, "dur": 49, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330549664, "dur": 1, "ph": "X", "name": "ProcessMessages 654", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330549666, "dur": 69, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330549738, "dur": 1, "ph": "X", "name": "ProcessMessages 465", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330549741, "dur": 50, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330549794, "dur": 1, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330549797, "dur": 44, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330549844, "dur": 1, "ph": "X", "name": "ProcessMessages 642", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330549846, "dur": 41, "ph": "X", "name": "ReadAsync 642", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330549890, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330549892, "dur": 39, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330549932, "dur": 1, "ph": "X", "name": "ProcessMessages 417", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330549934, "dur": 30, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330549968, "dur": 210, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330550182, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330550227, "dur": 1, "ph": "X", "name": "ProcessMessages 634", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330550229, "dur": 33, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330550265, "dur": 44, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330550312, "dur": 2, "ph": "X", "name": "ProcessMessages 518", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330550316, "dur": 54, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330550373, "dur": 2, "ph": "X", "name": "ProcessMessages 528", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330550376, "dur": 47, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330550426, "dur": 1, "ph": "X", "name": "ProcessMessages 220", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330550429, "dur": 50, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330550482, "dur": 1, "ph": "X", "name": "ProcessMessages 553", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330550484, "dur": 48, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330550535, "dur": 1, "ph": "X", "name": "ProcessMessages 666", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330550537, "dur": 56, "ph": "X", "name": "ReadAsync 666", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330550597, "dur": 1, "ph": "X", "name": "ProcessMessages 380", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330550600, "dur": 46, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330550649, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330550651, "dur": 69, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330550725, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330550728, "dur": 42, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330550773, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330550775, "dur": 38, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330550818, "dur": 46, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330550866, "dur": 1, "ph": "X", "name": "ProcessMessages 564", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330550869, "dur": 89, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330550960, "dur": 1, "ph": "X", "name": "ProcessMessages 360", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330550962, "dur": 104, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330551070, "dur": 2, "ph": "X", "name": "ProcessMessages 1007", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330551073, "dur": 67, "ph": "X", "name": "ReadAsync 1007", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330551143, "dur": 2, "ph": "X", "name": "ProcessMessages 1547", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330551146, "dur": 40, "ph": "X", "name": "ReadAsync 1547", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330551189, "dur": 1, "ph": "X", "name": "ProcessMessages 664", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330551190, "dur": 84, "ph": "X", "name": "ReadAsync 664", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330551280, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330551338, "dur": 1, "ph": "X", "name": "ProcessMessages 1293", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330551341, "dur": 39, "ph": "X", "name": "ReadAsync 1293", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330551382, "dur": 1, "ph": "X", "name": "ProcessMessages 317", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330551384, "dur": 41, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330551427, "dur": 1, "ph": "X", "name": "ProcessMessages 495", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330551429, "dur": 46, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330551478, "dur": 47, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330551527, "dur": 1, "ph": "X", "name": "ProcessMessages 540", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330551529, "dur": 45, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330551576, "dur": 1, "ph": "X", "name": "ProcessMessages 348", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330551578, "dur": 41, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330551623, "dur": 40, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330551667, "dur": 61, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330551730, "dur": 1, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330551732, "dur": 40, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330551775, "dur": 1, "ph": "X", "name": "ProcessMessages 503", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330551777, "dur": 30, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330551810, "dur": 41, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330551854, "dur": 47, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330551904, "dur": 1, "ph": "X", "name": "ProcessMessages 759", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330551906, "dur": 40, "ph": "X", "name": "ReadAsync 759", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330551948, "dur": 1, "ph": "X", "name": "ProcessMessages 545", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330551950, "dur": 35, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330551987, "dur": 1, "ph": "X", "name": "ProcessMessages 515", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330551990, "dur": 36, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330552029, "dur": 44, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330552076, "dur": 1, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330552078, "dur": 42, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330552123, "dur": 1, "ph": "X", "name": "ProcessMessages 491", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330552125, "dur": 37, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330552165, "dur": 37, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330552205, "dur": 43, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330552251, "dur": 1, "ph": "X", "name": "ProcessMessages 449", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330552253, "dur": 40, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330552296, "dur": 1, "ph": "X", "name": "ProcessMessages 650", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330552298, "dur": 35, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330552336, "dur": 36, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330552374, "dur": 1, "ph": "X", "name": "ProcessMessages 345", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330552375, "dur": 36, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330552414, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330552416, "dur": 39, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330552458, "dur": 42, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330552503, "dur": 2, "ph": "X", "name": "ProcessMessages 388", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330552505, "dur": 42, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330552549, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330552551, "dur": 33, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330552586, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330552588, "dur": 41, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330552631, "dur": 1, "ph": "X", "name": "ProcessMessages 616", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330552633, "dur": 36, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330552673, "dur": 49, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330552723, "dur": 1, "ph": "X", "name": "ProcessMessages 350", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330552725, "dur": 36, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330552763, "dur": 1, "ph": "X", "name": "ProcessMessages 441", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330552764, "dur": 33, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330552801, "dur": 42, "ph": "X", "name": "ReadAsync 223", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330552845, "dur": 1, "ph": "X", "name": "ProcessMessages 327", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330552847, "dur": 49, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330552899, "dur": 1, "ph": "X", "name": "ProcessMessages 713", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330552902, "dur": 39, "ph": "X", "name": "ReadAsync 713", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330552943, "dur": 1, "ph": "X", "name": "ProcessMessages 293", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330552945, "dur": 49, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330552998, "dur": 1, "ph": "X", "name": "ProcessMessages 628", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330553001, "dur": 41, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330553045, "dur": 1, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330553046, "dur": 50, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330553099, "dur": 1, "ph": "X", "name": "ProcessMessages 545", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330553102, "dur": 45, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330553151, "dur": 1, "ph": "X", "name": "ProcessMessages 417", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330553153, "dur": 46, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330553202, "dur": 1, "ph": "X", "name": "ProcessMessages 428", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330553205, "dur": 47, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330553254, "dur": 1, "ph": "X", "name": "ProcessMessages 537", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330553256, "dur": 41, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330553300, "dur": 1, "ph": "X", "name": "ProcessMessages 159", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330553302, "dur": 58, "ph": "X", "name": "ReadAsync 159", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330553363, "dur": 1, "ph": "X", "name": "ProcessMessages 321", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330553365, "dur": 54, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330553421, "dur": 1, "ph": "X", "name": "ProcessMessages 753", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330553424, "dur": 44, "ph": "X", "name": "ReadAsync 753", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330553471, "dur": 1, "ph": "X", "name": "ProcessMessages 387", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330553482, "dur": 75, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330553561, "dur": 1, "ph": "X", "name": "ProcessMessages 504", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330553564, "dur": 43, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330553610, "dur": 1, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330553612, "dur": 57, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330553672, "dur": 1, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330553674, "dur": 48, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330553726, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330553729, "dur": 48, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330553780, "dur": 1, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330553783, "dur": 48, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330553833, "dur": 1, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330553836, "dur": 47, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330553886, "dur": 1, "ph": "X", "name": "ProcessMessages 284", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330553888, "dur": 48, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330553939, "dur": 1, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330553941, "dur": 44, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330553988, "dur": 1, "ph": "X", "name": "ProcessMessages 479", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330553991, "dur": 52, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330554046, "dur": 1, "ph": "X", "name": "ProcessMessages 465", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330554047, "dur": 34, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330554086, "dur": 1, "ph": "X", "name": "ProcessMessages 504", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330554089, "dur": 38, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330554129, "dur": 3, "ph": "X", "name": "ProcessMessages 268", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330554134, "dur": 51, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330554188, "dur": 1, "ph": "X", "name": "ProcessMessages 383", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330554190, "dur": 49, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330554241, "dur": 1, "ph": "X", "name": "ProcessMessages 433", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330554244, "dur": 53, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330554301, "dur": 2, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330554304, "dur": 59, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330554366, "dur": 1, "ph": "X", "name": "ProcessMessages 581", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330554369, "dur": 52, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330554425, "dur": 1, "ph": "X", "name": "ProcessMessages 595", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330554428, "dur": 48, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330554478, "dur": 1, "ph": "X", "name": "ProcessMessages 510", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330554482, "dur": 52, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330554537, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330554539, "dur": 52, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330554594, "dur": 1, "ph": "X", "name": "ProcessMessages 473", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330554597, "dur": 47, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330554647, "dur": 1, "ph": "X", "name": "ProcessMessages 458", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330554649, "dur": 47, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330554698, "dur": 1, "ph": "X", "name": "ProcessMessages 612", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330554701, "dur": 47, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330554751, "dur": 1, "ph": "X", "name": "ProcessMessages 527", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330554753, "dur": 47, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330554804, "dur": 1, "ph": "X", "name": "ProcessMessages 395", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330554807, "dur": 40, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330554850, "dur": 1, "ph": "X", "name": "ProcessMessages 175", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330554854, "dur": 44, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330554901, "dur": 1, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330554904, "dur": 45, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330554951, "dur": 1, "ph": "X", "name": "ProcessMessages 522", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330554953, "dur": 49, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330555006, "dur": 1, "ph": "X", "name": "ProcessMessages 632", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330555009, "dur": 54, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330555066, "dur": 1, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330555069, "dur": 43, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330555117, "dur": 44, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330555164, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330555167, "dur": 45, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330555214, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330555216, "dur": 46, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330555265, "dur": 1, "ph": "X", "name": "ProcessMessages 705", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330555268, "dur": 46, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330555316, "dur": 1, "ph": "X", "name": "ProcessMessages 569", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330555319, "dur": 51, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330555372, "dur": 1, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330555375, "dur": 45, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330555422, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330555425, "dur": 40, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330555468, "dur": 1, "ph": "X", "name": "ProcessMessages 263", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330555470, "dur": 45, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330555517, "dur": 1, "ph": "X", "name": "ProcessMessages 550", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330555520, "dur": 43, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330555566, "dur": 1, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330555568, "dur": 44, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330555614, "dur": 1, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330555617, "dur": 46, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330555665, "dur": 1, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330555667, "dur": 34, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330555704, "dur": 1, "ph": "X", "name": "ProcessMessages 550", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330555705, "dur": 34, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330555742, "dur": 34, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330555779, "dur": 33, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330555814, "dur": 1, "ph": "X", "name": "ProcessMessages 344", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330555816, "dur": 36, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330555855, "dur": 33, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330555891, "dur": 35, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330555930, "dur": 29, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330555963, "dur": 36, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330556002, "dur": 33, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330556038, "dur": 33, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330556075, "dur": 79, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330556156, "dur": 1, "ph": "X", "name": "ProcessMessages 347", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330556158, "dur": 55, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330556216, "dur": 2, "ph": "X", "name": "ProcessMessages 919", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330556220, "dur": 40, "ph": "X", "name": "ReadAsync 919", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330556261, "dur": 1, "ph": "X", "name": "ProcessMessages 147", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330556263, "dur": 35, "ph": "X", "name": "ReadAsync 147", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330556301, "dur": 1, "ph": "X", "name": "ProcessMessages 215", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330556303, "dur": 280, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330556588, "dur": 83, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330556675, "dur": 3, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330556680, "dur": 33, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330556715, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330556717, "dur": 73, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330556795, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330556798, "dur": 236, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330557037, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330557041, "dur": 56, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330557100, "dur": 5, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330557107, "dur": 1408, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330558520, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330558524, "dur": 795, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330559330, "dur": 14, "ph": "X", "name": "ProcessMessages 1184", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330559345, "dur": 190, "ph": "X", "name": "ReadAsync 1184", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330559540, "dur": 7, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330559549, "dur": 61, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330559616, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330559620, "dur": 342, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330559966, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330559969, "dur": 190, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330560165, "dur": 5, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330560172, "dur": 47, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330560224, "dur": 27, "ph": "X", "name": "ProcessMessages 130", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330560254, "dur": 67, "ph": "X", "name": "ReadAsync 130", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330560327, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330560377, "dur": 436, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330560821, "dur": 107, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330560932, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330560936, "dur": 1044, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330561986, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330561991, "dur": 46, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330562039, "dur": 42, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330562083, "dur": 2865, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330564971, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330564976, "dur": 5293, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330570284, "dur": 60, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330570348, "dur": 9210, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330579570, "dur": 19679, "ph": "X", "name": "ProcessMessages 178", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330599259, "dur": 3962, "ph": "X", "name": "ReadAsync 178", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330603230, "dur": 23582, "ph": "X", "name": "ProcessMessages 2242", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330626820, "dur": 4520, "ph": "X", "name": "ReadAsync 2242", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330631348, "dur": 63, "ph": "X", "name": "ProcessMessages 46", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330631415, "dur": 63327, "ph": "X", "name": "ReadAsync 46", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330694751, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330694756, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330694790, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330694795, "dur": 5113, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330699916, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330699918, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330699978, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885330699982, "dur": 2331174, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885333031166, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885333031170, "dur": 209, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885333031384, "dur": 24, "ph": "X", "name": "ProcessMessages 333", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885333031411, "dur": 22091, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885333053513, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885333053518, "dur": 121, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885333053648, "dur": 29, "ph": "X", "name": "ProcessMessages 317", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885333053695, "dur": 37247, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885333090955, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885333090959, "dur": 93, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885333091061, "dur": 34, "ph": "X", "name": "ProcessMessages 86", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885333091098, "dur": 13610, "ph": "X", "name": "ReadAsync 86", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885333104717, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885333104721, "dur": 79, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885333104806, "dur": 3, "ph": "X", "name": "ProcessMessages 24", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885333104811, "dur": 806, "ph": "X", "name": "ReadAsync 24", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885333105622, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885333105625, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885333105670, "dur": 27, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885333105698, "dur": 17143, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885333122851, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885333122856, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885333122913, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885333122918, "dur": 59883, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885333182810, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885333182814, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885333182880, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885333182885, "dur": 1100, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885333183991, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885333183995, "dur": 128, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885333184128, "dur": 25, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885333184155, "dur": 81539, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885333265703, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885333265708, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885333265768, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885333265773, "dur": 1195, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885333266975, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885333266978, "dur": 72, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885333267054, "dur": 37, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885333267093, "dur": 644713, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885333911814, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885333911817, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885333911868, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885333911912, "dur": 37, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885333911952, "dur": 16, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885333911970, "dur": 76812, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885333988791, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885333988795, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885333988850, "dur": 3, "ph": "X", "name": "ProcessMessages 24", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885333988855, "dur": 81, "ph": "X", "name": "ReadAsync 24", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885333988940, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885333988942, "dur": 93, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885333989039, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885333989041, "dur": 942, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885333989990, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885333989993, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885333990064, "dur": 20, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885333990086, "dur": 30417, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885334020512, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885334020515, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885334020573, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885334020576, "dur": 64010, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885334084602, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885334084606, "dur": 80, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885334084692, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885334084697, "dur": 1314, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885334086020, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885334086024, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885334086079, "dur": 38, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885334086120, "dur": 231833, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885334317960, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885334317964, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885334317996, "dur": 24, "ph": "X", "name": "ProcessMessages 317", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885334318021, "dur": 36891, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885334354922, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885334354926, "dur": 551, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885334355483, "dur": 5, "ph": "X", "name": "ProcessMessages 120", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885334355491, "dur": 12943, "ph": "X", "name": "ReadAsync 120", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885334368442, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885334368446, "dur": 3045, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885334371501, "dur": 26, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885334371530, "dur": 45708, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885334417249, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885334417253, "dur": 720, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885334417980, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885334417989, "dur": 4643, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885334422641, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885334422645, "dur": 1483, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885334424137, "dur": 1384, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885334425532, "dur": 2685, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885334428228, "dur": 5, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885334428234, "dur": 1873, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885334430122, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885334430128, "dur": 2243, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885334432380, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885334432386, "dur": 11212, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885334443608, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885334443612, "dur": 2756, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885334446379, "dur": 6, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885334446387, "dur": 3301, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885334449699, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885334449704, "dur": 1912, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885334451627, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885334451634, "dur": 442832, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885334894477, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885334894483, "dur": 3006, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885334897500, "dur": 28, "ph": "X", "name": "ProcessMessages 70", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885334897530, "dur": 95535, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885334993075, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885334993079, "dur": 5998, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885334999088, "dur": 35, "ph": "X", "name": "ProcessMessages 371", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885334999126, "dur": 143666, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885335142802, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885335142806, "dur": 12598, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885335155415, "dur": 5, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885335155422, "dur": 184508, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885335339940, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885335339944, "dur": 634, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885335340584, "dur": 27, "ph": "X", "name": "ProcessMessages 391", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885335340612, "dur": 76599, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885335417221, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885335417225, "dur": 16778, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885335434021, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885335434028, "dur": 72807, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885335506845, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885335506849, "dur": 19706, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885335526565, "dur": 30, "ph": "X", "name": "ProcessMessages 411", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885335526599, "dur": 64207, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885335590823, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885335590828, "dur": 12459, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885335603296, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885335603300, "dur": 75178, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885335678489, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885335678494, "dur": 24972, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885335703477, "dur": 36, "ph": "X", "name": "ProcessMessages 331", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885335703516, "dur": 151359, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885335854885, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885335854890, "dur": 36366, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885335891266, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885335891273, "dur": 45838, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885335937122, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885335937127, "dur": 12341, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885335949477, "dur": 33, "ph": "X", "name": "ProcessMessages 333", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885335949512, "dur": 40187, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885335989709, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885335989713, "dur": 12875, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885336002599, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885336002605, "dur": 117786, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885336120401, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885336120406, "dur": 2732, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885336123150, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885336123156, "dur": 37324, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885336160489, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885336160494, "dur": 26513, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885336187018, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885336187025, "dur": 34064, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885336221100, "dur": 5, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885336221108, "dur": 55230, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885336276348, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885336276353, "dur": 49207, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885336325572, "dur": 7, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885336325582, "dur": 1104219, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885337429813, "dur": 7, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885337429822, "dur": 42761, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885337472593, "dur": 50, "ph": "X", "name": "ProcessMessages 347", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885337472645, "dur": 35448, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885337508105, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885337508109, "dur": 17411, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885337525528, "dur": 27, "ph": "X", "name": "ProcessMessages 323", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885337525557, "dur": 21269, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885337546836, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885337546840, "dur": 70191, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885337617043, "dur": 28, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885337617073, "dur": 323816, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885337940898, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885337940902, "dur": 16906, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885337957818, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885337957823, "dur": 184636, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885338142469, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885338142473, "dur": 5804, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885338148287, "dur": 42, "ph": "X", "name": "ProcessMessages 359", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885338148331, "dur": 67390, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885338215732, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885338215736, "dur": 13767, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885338229514, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885338229520, "dur": 37854, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885338267384, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885338267389, "dur": 10310, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885338277707, "dur": 5, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885338277714, "dur": 199216, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885338476939, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885338476943, "dur": 11500, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885338488459, "dur": 73, "ph": "X", "name": "ProcessMessages 266", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885338488534, "dur": 5410, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885338493952, "dur": 101, "ph": "X", "name": "ProcessMessages 562", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885338494055, "dur": 10676, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885338504741, "dur": 53, "ph": "X", "name": "ProcessMessages 218", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885338504797, "dur": 8704, "ph": "X", "name": "ReadAsync 218", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885338513510, "dur": 116, "ph": "X", "name": "ProcessMessages 178", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885338513630, "dur": 771076, "ph": "X", "name": "ReadAsync 178", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885339284715, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885339284720, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885339284759, "dur": 97, "ph": "X", "name": "ProcessMessages 339", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885339284858, "dur": 499421, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885339784384, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885339784390, "dur": 12413, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885339796812, "dur": 241, "ph": "X", "name": "ProcessMessages 136", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885339797120, "dur": 850543, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885340647687, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885340647692, "dur": 5521, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885340653223, "dur": 30, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885340653255, "dur": 17035, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885340670300, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885340670305, "dur": 8393, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885340678715, "dur": 26, "ph": "X", "name": "ProcessMessages 335", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885340678744, "dur": 73552, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885340752307, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885340752312, "dur": 96, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885340752412, "dur": 69, "ph": "X", "name": "ProcessMessages 337", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885340752484, "dur": 6926, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885340759420, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885340759424, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885340759463, "dur": 3, "ph": "X", "name": "ProcessMessages 24", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885340759468, "dur": 86, "ph": "X", "name": "ReadAsync 24", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885340759559, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885340759563, "dur": 1163, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885340760736, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885340760740, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885340760803, "dur": 134, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885340760940, "dur": 35790, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885340796739, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885340796744, "dur": 1395, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885340798149, "dur": 107, "ph": "X", "name": "ProcessMessages 120", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885340798259, "dur": 9721, "ph": "X", "name": "ReadAsync 120", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885340807989, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885340807994, "dur": 768, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885340808769, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885340808774, "dur": 28996, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885340837779, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885340837783, "dur": 1935, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885340839727, "dur": 41, "ph": "X", "name": "ProcessMessages 405", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885340839770, "dur": 47263, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885340887043, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885340887047, "dur": 2682, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885340889740, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885340889746, "dur": 25531, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885340915286, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885340915290, "dur": 3243, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885340918544, "dur": 34, "ph": "X", "name": "ProcessMessages 134", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885340918581, "dur": 46066, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885340964722, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885340964728, "dur": 869, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885340965606, "dur": 6, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885340965615, "dur": 5620, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885340971244, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885340971250, "dur": 397, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885340971654, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885340971660, "dur": 10114, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885340981785, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885340981790, "dur": 457, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885340982252, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885340982259, "dur": 116373, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885341098666, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885341098671, "dur": 240, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885341098918, "dur": 27, "ph": "X", "name": "ProcessMessages 371", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885341098947, "dur": 32239, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885341131194, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885341131199, "dur": 12603, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885341143811, "dur": 28, "ph": "X", "name": "ProcessMessages 54", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885341143841, "dur": 85664, "ph": "X", "name": "ReadAsync 54", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885341229514, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885341229518, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885341229551, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885341229555, "dur": 81099, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885341310664, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885341310668, "dur": 348, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885341311021, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885341311025, "dur": 134019, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885341445054, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885341445058, "dur": 992, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885341446059, "dur": 133, "ph": "X", "name": "ProcessMessages 331", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885341446195, "dur": 37586, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885341483792, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885341483796, "dur": 84726, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885341568536, "dur": 27, "ph": "X", "name": "ProcessMessages 337", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885341568566, "dur": 5965, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885341574539, "dur": 155, "ph": "X", "name": "ProcessMessages 66", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885341574698, "dur": 45311, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885341620019, "dur": 136, "ph": "X", "name": "ProcessMessages 66", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885341620158, "dur": 149459, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885341769627, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885341769632, "dur": 135, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885341769771, "dur": 32, "ph": "X", "name": "ProcessMessages 70", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885341769807, "dur": 11922, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885341781739, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885341781743, "dur": 2151, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885341783906, "dur": 160, "ph": "X", "name": "ProcessMessages 120", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885341784070, "dur": 43956, "ph": "X", "name": "ReadAsync 120", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885341828035, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885341828040, "dur": 1857, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885341829907, "dur": 138, "ph": "X", "name": "ProcessMessages 54", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885341830048, "dur": 16577, "ph": "X", "name": "ReadAsync 54", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885341846635, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885341846655, "dur": 209, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885341846869, "dur": 27, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885341846898, "dur": 82317, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885341929224, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885341929229, "dur": 8160, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885341937399, "dur": 125, "ph": "X", "name": "ProcessMessages 335", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885341937528, "dur": 26066, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885341963604, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885341963608, "dur": 4772, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885341968393, "dur": 143, "ph": "X", "name": "ProcessMessages 54", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885341968539, "dur": 7740, "ph": "X", "name": "ReadAsync 54", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885341976290, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885341976294, "dur": 641, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885341976941, "dur": 123, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885341977066, "dur": 23023, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885342000100, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885342000105, "dur": 230, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885342000340, "dur": 7, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885342000349, "dur": 303, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885342000662, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885342000665, "dur": 223, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885342000891, "dur": 35, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885342000929, "dur": 7168, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885342008116, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885342008121, "dur": 761, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885342008889, "dur": 28, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885342008918, "dur": 55785, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885342064714, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885342064718, "dur": 22041, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885342086770, "dur": 99, "ph": "X", "name": "ProcessMessages 120", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885342086872, "dur": 207392, "ph": "X", "name": "ReadAsync 120", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885342294274, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885342294279, "dur": 4351, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885342298640, "dur": 28, "ph": "X", "name": "ProcessMessages 54", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885342298671, "dur": 528816, "ph": "X", "name": "ReadAsync 54", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885342827495, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885342827500, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885342827559, "dur": 28, "ph": "X", "name": "ProcessMessages 341", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885342827589, "dur": 85646, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885342913245, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885342913249, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885342913309, "dur": 3, "ph": "X", "name": "ProcessMessages 24", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885342913314, "dur": 83, "ph": "X", "name": "ReadAsync 24", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885342913402, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885342913469, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885342913471, "dur": 1097, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885342914578, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885342914581, "dur": 73, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885342914661, "dur": 23, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885342914686, "dur": 15651, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885342930349, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885342930374, "dur": 259, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885342930655, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885342930661, "dur": 1015, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885342931684, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885342931687, "dur": 157, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885342931849, "dur": 26, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885342931878, "dur": 20890, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885342952779, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885342952783, "dur": 129, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885342952918, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885342952924, "dur": 51, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885342952979, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885342952982, "dur": 1354219, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885344307211, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885344307215, "dur": 129, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885344307349, "dur": 26, "ph": "X", "name": "ProcessMessages 341", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885344307377, "dur": 92446, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885344399833, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885344399838, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885344399899, "dur": 4, "ph": "X", "name": "ProcessMessages 24", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885344399905, "dur": 47, "ph": "X", "name": "ReadAsync 24", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885344399955, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885344399957, "dur": 816, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885344400865, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885344400868, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885344400927, "dur": 76, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885344401005, "dur": 29070, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885344430085, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885344430089, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885344430143, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885344430148, "dur": 2949, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885344433108, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885344433113, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885344433180, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885344433185, "dur": 1337, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885344434532, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885344434535, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885344434596, "dur": 27, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885344434625, "dur": 245272, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885344679906, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885344679910, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885344679962, "dur": 82, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885344680046, "dur": 77867, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885344757923, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885344757927, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885344757981, "dur": 4, "ph": "X", "name": "ProcessMessages 24", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885344757987, "dur": 742, "ph": "X", "name": "ReadAsync 24", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885344758735, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885344758737, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885344758788, "dur": 25, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885344758816, "dur": 9478, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885344768304, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885344768307, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885344768359, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885344768364, "dur": 833, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885344769202, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885344769206, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885344769259, "dur": 174, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885344769436, "dur": 29354, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885344798801, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885344798805, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885344798859, "dur": 24, "ph": "X", "name": "ProcessMessages 355", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885344798885, "dur": 46705, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885344845598, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885344845602, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885344845663, "dur": 4, "ph": "X", "name": "ProcessMessages 24", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885344845668, "dur": 142, "ph": "X", "name": "ReadAsync 24", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885344845815, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885344845817, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885344845865, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885344845867, "dur": 1191, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885344847066, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885344847070, "dur": 143, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885344847217, "dur": 29, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885344847248, "dur": 30923, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885344878181, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885344878185, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885344878232, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885344878236, "dur": 83952, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885344962198, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885344962203, "dur": 157, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885344962367, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885344962374, "dur": 2283, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885344964669, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885344964673, "dur": 517, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885344965196, "dur": 26, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885344965224, "dur": 502067, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885345467301, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885345467305, "dur": 126, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885345467437, "dur": 29, "ph": "X", "name": "ProcessMessages 355", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885345467468, "dur": 97729, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885345565209, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885345565213, "dur": 96, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885345565314, "dur": 5, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885345565320, "dur": 867, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885345566196, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885345566200, "dur": 70, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885345566274, "dur": 24, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885345566300, "dur": 40607, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885345606916, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885345606921, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885345606980, "dur": 3, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885345606985, "dur": 37285, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885345644282, "dur": 54, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885345644339, "dur": 353, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885345644698, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885345644704, "dur": 2183, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885345646896, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885345646900, "dur": 93, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885345646996, "dur": 26, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885345647024, "dur": 209179, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885345856214, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885345856219, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885345856277, "dur": 24, "ph": "X", "name": "ProcessMessages 379", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885345856303, "dur": 40498, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885345896811, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885345896815, "dur": 132, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885345896952, "dur": 5, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885345896959, "dur": 1038, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885345898006, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885345898010, "dur": 170, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885345898185, "dur": 33, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885345898221, "dur": 52304, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885345950534, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885345950539, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885345950606, "dur": 25, "ph": "X", "name": "ProcessMessages 357", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885345950632, "dur": 48285, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885345998927, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885345998932, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885345998997, "dur": 27, "ph": "X", "name": "ProcessMessages 86", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885345999026, "dur": 35322, "ph": "X", "name": "ReadAsync 86", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885346034362, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885346034367, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885346034413, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885346034418, "dur": 18697, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885346053125, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885346053129, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885346053177, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885346053181, "dur": 1082, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885346054272, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885346054276, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885346054333, "dur": 40, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885346054376, "dur": 130369, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885346184755, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885346184759, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885346184821, "dur": 24, "ph": "X", "name": "ProcessMessages 359", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885346184846, "dur": 34427, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885346219283, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885346219287, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885346219354, "dur": 3, "ph": "X", "name": "ProcessMessages 24", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885346219358, "dur": 121, "ph": "X", "name": "ReadAsync 24", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885346219486, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885346219489, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885346219522, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885346219524, "dur": 655, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885346220184, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885346220186, "dur": 70, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885346220260, "dur": 28, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885346220291, "dur": 342, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885346220639, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885346220641, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885346220706, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885346220710, "dur": 4457, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885346225175, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885346225178, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885346225237, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885346225242, "dur": 959, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885346226206, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885346226209, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885346226269, "dur": 26, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885346226297, "dur": 147811, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885346374231, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885346374237, "dur": 79, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885346374321, "dur": 24, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885346374347, "dur": 3293, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885346377649, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885346377654, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885346377722, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885346377726, "dur": 1031, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885346378764, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885346378766, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885346378805, "dur": 32, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885346378839, "dur": 20289, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885346399138, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885346399143, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885346399204, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885346399209, "dur": 1620, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885346400838, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885346400844, "dur": 101, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885346400950, "dur": 26, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885346400978, "dur": 573, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885346401555, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885346401558, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885346401615, "dur": 1, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 20696, "tid": 21474836480, "ts": 1751885346401617, "dur": 8810, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 20696, "tid": 18, "ts": 1751885346433460, "dur": 1760, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 20696, "tid": 17179869184, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 20696, "tid": 17179869184, "ts": 1751885330473358, "dur": 22, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 20696, "tid": 17179869184, "ts": 1751885330473381, "dur": 19611, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 20696, "tid": 17179869184, "ts": 1751885330492993, "dur": 45, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 20696, "tid": 18, "ts": 1751885346435222, "dur": 31, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 20696, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 20696, "tid": 1, "ts": 1751885329781223, "dur": 13213, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 20696, "tid": 1, "ts": 1751885329794443, "dur": 129296, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 20696, "tid": 1, "ts": 1751885329923746, "dur": 44411, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 20696, "tid": 18, "ts": 1751885346435255, "dur": 4, "ph": "X", "name": "", "args": {}}, {"pid": 20696, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329767621, "dur": 2797, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329770422, "dur": 208929, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329771959, "dur": 6357, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329778326, "dur": 3188, "ph": "X", "name": "ProcessMessages 20496", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329781522, "dur": 572, "ph": "X", "name": "ReadAsync 20496", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329782099, "dur": 30, "ph": "X", "name": "ProcessMessages 20525", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329782131, "dur": 70, "ph": "X", "name": "ReadAsync 20525", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329782206, "dur": 2, "ph": "X", "name": "ProcessMessages 489", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329782210, "dur": 57, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329782271, "dur": 2, "ph": "X", "name": "ProcessMessages 417", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329782275, "dur": 47, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329782327, "dur": 1, "ph": "X", "name": "ProcessMessages 383", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329782330, "dur": 46, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329782380, "dur": 1, "ph": "X", "name": "ProcessMessages 165", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329782383, "dur": 118, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329782506, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329782554, "dur": 1, "ph": "X", "name": "ProcessMessages 365", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329782556, "dur": 57, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329782617, "dur": 1, "ph": "X", "name": "ProcessMessages 324", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329782620, "dur": 70, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329782693, "dur": 4, "ph": "X", "name": "ProcessMessages 397", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329782698, "dur": 96, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329782799, "dur": 2, "ph": "X", "name": "ProcessMessages 235", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329782802, "dur": 70, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329782875, "dur": 1, "ph": "X", "name": "ProcessMessages 258", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329782878, "dur": 217, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329783097, "dur": 1, "ph": "X", "name": "ProcessMessages 262", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329783100, "dur": 77, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329783179, "dur": 3, "ph": "X", "name": "ProcessMessages 1583", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329783183, "dur": 47, "ph": "X", "name": "ReadAsync 1583", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329783244, "dur": 1, "ph": "X", "name": "ProcessMessages 249", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329783247, "dur": 94, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329783345, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329783348, "dur": 135, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329783486, "dur": 2, "ph": "X", "name": "ProcessMessages 959", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329783490, "dur": 46, "ph": "X", "name": "ReadAsync 959", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329783538, "dur": 1, "ph": "X", "name": "ProcessMessages 829", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329783541, "dur": 95, "ph": "X", "name": "ReadAsync 829", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329783843, "dur": 1, "ph": "X", "name": "ProcessMessages 245", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329783847, "dur": 147, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329783997, "dur": 4, "ph": "X", "name": "ProcessMessages 2569", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329784003, "dur": 743, "ph": "X", "name": "ReadAsync 2569", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329784752, "dur": 3, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329784813, "dur": 219, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329785036, "dur": 10, "ph": "X", "name": "ProcessMessages 8325", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329785047, "dur": 125, "ph": "X", "name": "ReadAsync 8325", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329785222, "dur": 2, "ph": "X", "name": "ProcessMessages 976", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329785226, "dur": 55, "ph": "X", "name": "ReadAsync 976", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329785284, "dur": 2, "ph": "X", "name": "ProcessMessages 506", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329785288, "dur": 54, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329785346, "dur": 1, "ph": "X", "name": "ProcessMessages 380", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329785348, "dur": 30, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329785381, "dur": 1, "ph": "X", "name": "ProcessMessages 155", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329785384, "dur": 40, "ph": "X", "name": "ReadAsync 155", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329785429, "dur": 43, "ph": "X", "name": "ReadAsync 226", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329785477, "dur": 2, "ph": "X", "name": "ProcessMessages 294", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329785480, "dur": 117, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329785601, "dur": 3, "ph": "X", "name": "ProcessMessages 1071", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329785605, "dur": 44, "ph": "X", "name": "ReadAsync 1071", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329785651, "dur": 1, "ph": "X", "name": "ProcessMessages 262", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329785734, "dur": 114, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329785851, "dur": 2, "ph": "X", "name": "ProcessMessages 974", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329785854, "dur": 58, "ph": "X", "name": "ReadAsync 974", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329785915, "dur": 2, "ph": "X", "name": "ProcessMessages 1119", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329785919, "dur": 110, "ph": "X", "name": "ReadAsync 1119", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329786032, "dur": 1, "ph": "X", "name": "ProcessMessages 951", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329786035, "dur": 46, "ph": "X", "name": "ReadAsync 951", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329786084, "dur": 2, "ph": "X", "name": "ProcessMessages 387", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329786088, "dur": 47, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329786139, "dur": 1, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329786142, "dur": 47, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329786192, "dur": 1, "ph": "X", "name": "ProcessMessages 358", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329786195, "dur": 82, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329786281, "dur": 288, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329786574, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329786577, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329786639, "dur": 1, "ph": "X", "name": "ProcessMessages 613", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329786642, "dur": 56, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329786701, "dur": 2, "ph": "X", "name": "ProcessMessages 349", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329786705, "dur": 121, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329786829, "dur": 1, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329786831, "dur": 59, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329786963, "dur": 2, "ph": "X", "name": "ProcessMessages 976", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329786967, "dur": 212, "ph": "X", "name": "ReadAsync 976", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329787183, "dur": 3, "ph": "X", "name": "ProcessMessages 2136", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329787188, "dur": 54, "ph": "X", "name": "ReadAsync 2136", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329787245, "dur": 1, "ph": "X", "name": "ProcessMessages 612", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329787249, "dur": 46, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329787298, "dur": 970, "ph": "X", "name": "ProcessMessages 331", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329788279, "dur": 218, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329788503, "dur": 17, "ph": "X", "name": "ProcessMessages 12443", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329788522, "dur": 51, "ph": "X", "name": "ReadAsync 12443", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329788575, "dur": 1, "ph": "X", "name": "ProcessMessages 716", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329788578, "dur": 39, "ph": "X", "name": "ReadAsync 716", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329788619, "dur": 1, "ph": "X", "name": "ProcessMessages 250", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329788621, "dur": 49, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329788672, "dur": 1, "ph": "X", "name": "ProcessMessages 247", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329788674, "dur": 47, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329788724, "dur": 1, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329788727, "dur": 37, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329788766, "dur": 1, "ph": "X", "name": "ProcessMessages 65", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329788769, "dur": 49, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329788820, "dur": 1, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329788822, "dur": 49, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329788874, "dur": 1, "ph": "X", "name": "ProcessMessages 418", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329788876, "dur": 70, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329788949, "dur": 1, "ph": "X", "name": "ProcessMessages 532", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329788952, "dur": 58, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329789014, "dur": 2, "ph": "X", "name": "ProcessMessages 656", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329789017, "dur": 69, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329789089, "dur": 1, "ph": "X", "name": "ProcessMessages 252", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329789092, "dur": 98, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329789194, "dur": 1, "ph": "X", "name": "ProcessMessages 137", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329789197, "dur": 145, "ph": "X", "name": "ReadAsync 137", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329789346, "dur": 1, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329789349, "dur": 429, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329789781, "dur": 2, "ph": "X", "name": "ProcessMessages 1110", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329789785, "dur": 134, "ph": "X", "name": "ReadAsync 1110", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329789921, "dur": 5, "ph": "X", "name": "ProcessMessages 2636", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329789928, "dur": 42, "ph": "X", "name": "ReadAsync 2636", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329789971, "dur": 1, "ph": "X", "name": "ProcessMessages 740", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329789973, "dur": 31, "ph": "X", "name": "ReadAsync 740", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329790008, "dur": 107, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329790117, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329790122, "dur": 60, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329790187, "dur": 3, "ph": "X", "name": "ProcessMessages 1190", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329790191, "dur": 38, "ph": "X", "name": "ReadAsync 1190", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329790231, "dur": 1, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329790234, "dur": 40, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329790278, "dur": 1, "ph": "X", "name": "ProcessMessages 378", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329790281, "dur": 44, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329790328, "dur": 1, "ph": "X", "name": "ProcessMessages 582", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329790330, "dur": 37, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329790371, "dur": 2, "ph": "X", "name": "ProcessMessages 282", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329790374, "dur": 43, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329790421, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329790424, "dur": 44, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329790472, "dur": 1, "ph": "X", "name": "ProcessMessages 54", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329790475, "dur": 41, "ph": "X", "name": "ReadAsync 54", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329790518, "dur": 1, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329790520, "dur": 42, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329790589, "dur": 1, "ph": "X", "name": "ProcessMessages 281", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329790591, "dur": 56, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329790650, "dur": 2, "ph": "X", "name": "ProcessMessages 684", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329790654, "dur": 59, "ph": "X", "name": "ReadAsync 684", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329790717, "dur": 1, "ph": "X", "name": "ProcessMessages 465", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329790721, "dur": 61, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329790785, "dur": 1, "ph": "X", "name": "ProcessMessages 575", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329790788, "dur": 39, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329790831, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329790833, "dur": 57, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329790894, "dur": 2, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329790897, "dur": 48, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329790948, "dur": 1, "ph": "X", "name": "ProcessMessages 416", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329790950, "dur": 54, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329791008, "dur": 1, "ph": "X", "name": "ProcessMessages 408", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329791011, "dur": 50, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329791064, "dur": 1, "ph": "X", "name": "ProcessMessages 482", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329791066, "dur": 48, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329791118, "dur": 1, "ph": "X", "name": "ProcessMessages 220", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329791121, "dur": 48, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329791172, "dur": 1, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329791174, "dur": 56, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329791233, "dur": 1, "ph": "X", "name": "ProcessMessages 377", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329791236, "dur": 105, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329791345, "dur": 1, "ph": "X", "name": "ProcessMessages 446", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329791347, "dur": 71, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329791421, "dur": 2, "ph": "X", "name": "ProcessMessages 1137", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329791424, "dur": 48, "ph": "X", "name": "ReadAsync 1137", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329791474, "dur": 1, "ph": "X", "name": "ProcessMessages 377", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329791477, "dur": 45, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329791525, "dur": 1, "ph": "X", "name": "ProcessMessages 345", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329791527, "dur": 49, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329791579, "dur": 1, "ph": "X", "name": "ProcessMessages 307", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329791581, "dur": 49, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329791633, "dur": 1, "ph": "X", "name": "ProcessMessages 422", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329791636, "dur": 50, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329791688, "dur": 1, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329791691, "dur": 68, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329791761, "dur": 1, "ph": "X", "name": "ProcessMessages 666", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329791764, "dur": 41, "ph": "X", "name": "ReadAsync 666", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329791809, "dur": 1, "ph": "X", "name": "ProcessMessages 221", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329791811, "dur": 52, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329791866, "dur": 1, "ph": "X", "name": "ProcessMessages 477", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329791869, "dur": 56, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329791928, "dur": 1, "ph": "X", "name": "ProcessMessages 410", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329791931, "dur": 50, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329791984, "dur": 1, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329791986, "dur": 53, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329792042, "dur": 1, "ph": "X", "name": "ProcessMessages 541", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329792045, "dur": 59, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329792107, "dur": 11, "ph": "X", "name": "ProcessMessages 622", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329792119, "dur": 41, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329792165, "dur": 49, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329792217, "dur": 1, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329792220, "dur": 54, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329792279, "dur": 1, "ph": "X", "name": "ProcessMessages 568", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329792282, "dur": 51, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329792336, "dur": 3, "ph": "X", "name": "ProcessMessages 572", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329792341, "dur": 46, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329792390, "dur": 1, "ph": "X", "name": "ProcessMessages 289", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329792393, "dur": 52, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329792447, "dur": 1, "ph": "X", "name": "ProcessMessages 500", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329792450, "dur": 42, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329792496, "dur": 1, "ph": "X", "name": "ProcessMessages 190", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329792499, "dur": 50, "ph": "X", "name": "ReadAsync 190", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329792552, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329792554, "dur": 50, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329792608, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329792610, "dur": 52, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329792665, "dur": 1, "ph": "X", "name": "ProcessMessages 705", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329792667, "dur": 54, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329792724, "dur": 1, "ph": "X", "name": "ProcessMessages 609", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329792727, "dur": 49, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329792778, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329792781, "dur": 35, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329792818, "dur": 1, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329792821, "dur": 42, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329792876, "dur": 1, "ph": "X", "name": "ProcessMessages 263", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329792879, "dur": 44, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329792926, "dur": 3, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329792931, "dur": 49, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329792984, "dur": 1, "ph": "X", "name": "ProcessMessages 535", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329792988, "dur": 49, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329793039, "dur": 1, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329793042, "dur": 49, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329793094, "dur": 1, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329793097, "dur": 47, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329793148, "dur": 1, "ph": "X", "name": "ProcessMessages 409", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329793151, "dur": 48, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329793201, "dur": 1, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329793204, "dur": 51, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329793258, "dur": 1, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329793261, "dur": 160, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329793425, "dur": 2, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329793428, "dur": 88, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329793521, "dur": 3, "ph": "X", "name": "ProcessMessages 1384", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329793525, "dur": 55, "ph": "X", "name": "ReadAsync 1384", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329793584, "dur": 2, "ph": "X", "name": "ProcessMessages 656", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329793588, "dur": 45, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329793636, "dur": 2, "ph": "X", "name": "ProcessMessages 373", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329793640, "dur": 51, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329793694, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329793697, "dur": 60, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329793761, "dur": 1, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329793763, "dur": 96, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329793863, "dur": 1, "ph": "X", "name": "ProcessMessages 290", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329793866, "dur": 48, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329793919, "dur": 2, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329793922, "dur": 45, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329793971, "dur": 1, "ph": "X", "name": "ProcessMessages 215", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329793974, "dur": 1012, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329794991, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329794993, "dur": 81, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329795078, "dur": 415, "ph": "X", "name": "ProcessMessages 1220", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329795497, "dur": 365, "ph": "X", "name": "ReadAsync 1220", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329795867, "dur": 6, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329795883, "dur": 58, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329795948, "dur": 5, "ph": "X", "name": "ProcessMessages 416", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329795955, "dur": 447, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329796406, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329796409, "dur": 141, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329796555, "dur": 7, "ph": "X", "name": "ProcessMessages 656", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329796563, "dur": 335, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329796902, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329796906, "dur": 256, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329797168, "dur": 9, "ph": "X", "name": "ProcessMessages 784", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329797178, "dur": 292, "ph": "X", "name": "ReadAsync 784", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329797474, "dur": 4, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329797480, "dur": 742, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329798226, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329798229, "dur": 5063, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329803301, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329803308, "dur": 69, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329803382, "dur": 830, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329804218, "dur": 1976, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329806204, "dur": 4, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329806210, "dur": 167, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329806382, "dur": 4, "ph": "X", "name": "ProcessMessages 72", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329806387, "dur": 4069, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329810465, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329810469, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329810527, "dur": 4, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329810534, "dur": 677, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329811215, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329811219, "dur": 4007, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329815237, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329815245, "dur": 9079, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329824337, "dur": 14737, "ph": "X", "name": "ProcessMessages 430", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329839082, "dur": 7478, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329846570, "dur": 13524, "ph": "X", "name": "ProcessMessages 2030", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329860136, "dur": 2567, "ph": "X", "name": "ReadAsync 2030", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329862714, "dur": 724, "ph": "X", "name": "ProcessMessages 2538", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329863443, "dur": 1594, "ph": "X", "name": "ReadAsync 2538", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329865047, "dur": 133, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329865183, "dur": 793, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329865984, "dur": 28, "ph": "X", "name": "ProcessMessages 46", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329866015, "dur": 6057, "ph": "X", "name": "ReadAsync 46", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329872081, "dur": 38, "ph": "X", "name": "ProcessMessages 46", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329872121, "dur": 47, "ph": "X", "name": "ReadAsync 46", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329872172, "dur": 350, "ph": "X", "name": "ProcessMessages 154", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329872526, "dur": 98070, "ph": "X", "name": "ReadAsync 154", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329970604, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329970607, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329970656, "dur": 303, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751885329970963, "dur": 8107, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 20696, "tid": 18, "ts": 1751885346435261, "dur": 445, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 20696, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 20696, "tid": 8589934592, "ts": 1751885329752769, "dur": 215477, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 20696, "tid": 8589934592, "ts": 1751885329968249, "dur": 4, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 20696, "tid": 8589934592, "ts": 1751885329968254, "dur": 1514, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 20696, "tid": 18, "ts": 1751885346435708, "dur": 4, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 20696, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 20696, "tid": 4294967296, "ts": 1751885329553051, "dur": 428021, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 20696, "tid": 4294967296, "ts": 1751885329619561, "dur": 117357, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 20696, "tid": 4294967296, "ts": 1751885329981746, "dur": 487489, "ph": "X", "name": "await ExecuteBuildProgram", "args": {}}, {"pid": 20696, "tid": 4294967296, "ts": 1751885330469524, "dur": 15940949, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 20696, "tid": 4294967296, "ts": 1751885330469709, "dur": 3612, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 20696, "tid": 4294967296, "ts": 1751885346410499, "dur": 11473, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 20696, "tid": 4294967296, "ts": 1751885346414973, "dur": 4831, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 20696, "tid": 4294967296, "ts": 1751885346421979, "dur": 20, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 20696, "tid": 18, "ts": 1751885346435714, "dur": 17, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1751885330494328, "dur": 48255, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751885330542589, "dur": 195, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751885330542836, "dur": 52, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1751885330542888, "dur": 379, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751885330543590, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_AD3F2B6EFF130063.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751885330543738, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_7D12CF9E98D9D784.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751885330544007, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_78F0F79A0FB195D1.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751885330544378, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_86082FB9E11717DE.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751885330544602, "dur": 76, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_4DF46BE925392E19.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751885330544701, "dur": 89, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_12136BD48011B700.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751885330544855, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_DF7681D3753AB490.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751885330544973, "dur": 109, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_1AA34DA22D3674A5.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751885330545144, "dur": 84, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_8C103AE0E7B61272.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751885330545264, "dur": 83, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_42529D1BD0B2CBE0.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751885330545436, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_DB68F65C9F3573CD.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751885330545584, "dur": 87, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_40E8F641E9958680.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751885330545694, "dur": 98, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_656AB5FDB0417D9D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751885330545814, "dur": 93, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_3520FC5D8B4827F4.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751885330545930, "dur": 83, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_782156EAC64F1FC2.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751885330546147, "dur": 89, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_681D101710621A59.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751885330546312, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_EABB3BB0B4DAF932.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751885330546383, "dur": 82, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_FB065B49AB03584B.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751885330546612, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_7B05F0D16B4953B8.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751885330547003, "dur": 84, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_C7DF13E3C14DE501.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751885330547112, "dur": 80, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_177F4FE55DE2E02C.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751885330547219, "dur": 87, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_BD82F057A3253721.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751885330548618, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751885330551987, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751885330554189, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751885330554828, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751885330554941, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751885330543303, "dur": 14288, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751885330557608, "dur": 15844621, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751885346402230, "dur": 194, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751885346402424, "dur": 131, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751885346402748, "dur": 71, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751885346402847, "dur": 1696, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1751885330543739, "dur": 13899, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751885330557655, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_2784FA9BFF134207.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751885330557887, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_4DE8BB45CF6CF9CC.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751885330558337, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751885330558510, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_BD82F057A3253721.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751885330561022, "dur": 1292, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751885330566896, "dur": 38513, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751885330562319, "dur": 43092, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751885330606137, "dur": 89701, "ph": "X", "name": "InvokeCacheMe", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEngine.TestRunner.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751885330695848, "dur": 172, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751885333032984, "dur": 52132, "ph": "X", "name": "InvokeCacheMe", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEngine.TestRunner.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751885330697096, "dur": 2388029, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751885333088959, "dur": 306, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEngine.TestRunner.ref.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751885333088942, "dur": 327, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TestRunner.ref.dll_E55D0F7C63F01D9E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751885333089318, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751885333089495, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751885333090725, "dur": 303, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751885333089572, "dur": 1458, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751885333091894, "dur": 32042, "ph": "X", "name": "InvokeCacheMe", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEditor.TestRunner.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751885333123942, "dur": 151, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751885333914709, "dur": 71520, "ph": "X", "name": "InvokeCacheMe", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEditor.TestRunner.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751885333125098, "dur": 861157, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751885333989360, "dur": 684, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEditor.TestRunner.ref.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751885333989345, "dur": 702, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TestRunner.ref.dll_193EC4CE382CBFB3.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751885333990078, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751885333990891, "dur": 255, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751885333990243, "dur": 904, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751885333991578, "dur": 30072, "ph": "X", "name": "InvokeCacheMe", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEditor.UI.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751885334021656, "dur": 78, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751885334319537, "dur": 33275, "ph": "X", "name": "InvokeCacheMe", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEditor.UI.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751885334022619, "dur": 330200, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751885334354814, "dur": 392, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEditor.UI.ref.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751885334354798, "dur": 411, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UI.ref.dll_4C98D3F7040CD4F5.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751885334355266, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751885334356671, "dur": 228, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751885334355453, "dur": 1447, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751885334357982, "dur": 71415, "ph": "X", "name": "InvokeCacheMe", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.InputSystem.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751885334429407, "dur": 272, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751885340834566, "dur": 63875, "ph": "X", "name": "InvokeCacheMe", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.InputSystem.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751885334431029, "dur": 6467420, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751885340905449, "dur": 862, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.InputSystem.ref.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751885340905433, "dur": 883, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.InputSystem.ref.dll_FD2EC87C14EBE081.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751885340906358, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751885340908156, "dur": 289, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751885340906570, "dur": 1876, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751885340909617, "dur": 69299, "ph": "X", "name": "InvokeCacheMe", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.Core.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751885340978925, "dur": 367, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751885342829230, "dur": 80190, "ph": "X", "name": "InvokeCacheMe", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.Core.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751885340980826, "dur": 1928601, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751885342914060, "dur": 451, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.Core.ref.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751885342914044, "dur": 470, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Core.ref.dll_D75DF4EA1AFE54F4.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751885342914548, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751885342916164, "dur": 274, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751885342914707, "dur": 1733, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751885342917529, "dur": 36503, "ph": "X", "name": "InvokeCacheMe", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.Flow.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751885342954036, "dur": 224, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751885344307004, "dur": 87970, "ph": "X", "name": "InvokeCacheMe", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.Flow.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751885342955610, "dur": 1439371, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751885344400689, "dur": 382, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.Flow.ref.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751885344400673, "dur": 402, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Flow.ref.dll_1E6ED5409D07C9A3.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751885344401111, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751885344401595, "dur": 216, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751885344401238, "dur": 576, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751885344402289, "dur": 28974, "ph": "X", "name": "InvokeCacheMe", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.State.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751885344431270, "dur": 70, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751885344681613, "dur": 75409, "ph": "X", "name": "InvokeCacheMe", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.State.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751885344432348, "dur": 324681, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751885344758890, "dur": 282, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.State.ref.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751885344758873, "dur": 301, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.State.ref.dll_D91762B7076BE462.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751885344759203, "dur": 87018, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751885344846221, "dur": 719597, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751885345565818, "dur": 331885, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751885345897703, "dur": 98517, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751885345996256, "dur": 352, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.State.Editor.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1751885345996223, "dur": 387, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.Editor.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1751885345996721, "dur": 1104, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.State.Editor.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1751885345997831, "dur": 222362, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751885346220194, "dur": 182004, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751885330543729, "dur": 13896, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751885330558630, "dur": 786, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "E:/Unity/Editior/6000.0.34f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 2, "ts": 1751885330557631, "dur": 1786, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751885330559517, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751885330561366, "dur": 4956, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751885330567255, "dur": 17026, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751885330584998, "dur": 1555, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751885330587278, "dur": 477, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751885330587757, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751885330588734, "dur": 679, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751885330590025, "dur": 451, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751885330591276, "dur": 1689, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751885330592967, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751885330593218, "dur": 402, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751885330593621, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751885330593875, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751885330594119, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751885330594348, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751885330594601, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751885330594818, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751885330595079, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751885330595318, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751885330595559, "dur": 352, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751885330595911, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751885330596166, "dur": 300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751885330596467, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751885330596723, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751885330596964, "dur": 295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751885330597260, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751885330597512, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751885330597744, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751885330597989, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751885330598232, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751885330598494, "dur": 334, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751885330598829, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751885330599075, "dur": 498, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751885330599573, "dur": 311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751885330600040, "dur": 665, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Profiling\\ProfiledSegmentCollection.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751885330599884, "dur": 902, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751885330600787, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751885330601006, "dur": 280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751885330601287, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751885330601495, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751885330601728, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751885330601933, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751885330602137, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751885330602348, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751885330602563, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751885330602799, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751885330603014, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751885330603257, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751885330603518, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751885330603801, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751885330604029, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751885330604232, "dur": 55, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751885330604402, "dur": 2484540, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751885333088975, "dur": 95084, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEngine.TestRunner.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751885333088944, "dur": 95117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751885333184083, "dur": 1214, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEngine.TestRunner.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751885333185301, "dur": 804048, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751885333989349, "dur": 365463, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751885334354812, "dur": 453, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751885334355267, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751885334356518, "dur": 377, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751885334356000, "dur": 896, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751885334357355, "dur": 67394, "ph": "X", "name": "InvokeCacheMe", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Multiplayer.Center.Common.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751885334424760, "dur": 122, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751885334969823, "dur": 164567, "ph": "X", "name": "InvokeCacheMe", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Multiplayer.Center.Common.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751885334425843, "dur": 708559, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751885335137127, "dur": 300, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Multiplayer.Center.Common.ref.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751885335137110, "dur": 321, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Common.ref.dll_EE36537354EA42C8.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751885335137467, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751885335137669, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751885335137850, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751885335138018, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751885335138192, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751885335139018, "dur": 323, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751885335138358, "dur": 985, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751885335139998, "dur": 262080, "ph": "X", "name": "InvokeCacheMe", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Rider.Editor.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751885335402087, "dur": 139, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751885337549341, "dur": 329196, "ph": "X", "name": "InvokeCacheMe", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Rider.Editor.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751885335404823, "dur": 2473722, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751885337882081, "dur": 259, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751885337881556, "dur": 785, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751885337882817, "dur": 385468, "ph": "X", "name": "InvokeCacheMe", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Multiplayer.Center.Editor.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751885338268294, "dur": 131, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751885340639256, "dur": 114060, "ph": "X", "name": "InvokeCacheMe", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Multiplayer.Center.Editor.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751885338269521, "dur": 2483806, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751885340756671, "dur": 3984, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Multiplayer.Center.Editor.ref.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751885340756653, "dur": 4007, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Editor.ref.dll_EB1F4B6E56116D4A.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751885340760697, "dur": 26434, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751885340787159, "dur": 805, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.PlasticSCM.Editor.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751885340787132, "dur": 834, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PlasticSCM.Editor.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751885340787981, "dur": 1538, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.PlasticSCM.Editor.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751885340789523, "dur": 46252, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751885340835776, "dur": 69662, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751885340905475, "dur": 799, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.InputSystem.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1751885340905445, "dur": 831, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1751885340906305, "dur": 1639, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.InputSystem.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1751885340907951, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751885340908170, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751885340908907, "dur": 209, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.InputSystem.TestFramework.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751885340908387, "dur": 731, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751885340909894, "dur": 55699, "ph": "X", "name": "InvokeCacheMe", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.InputSystem.TestFramework.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751885340965602, "dur": 158, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751885341847155, "dur": 103175, "ph": "X", "name": "InvokeCacheMe", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.InputSystem.TestFramework.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751885340966836, "dur": 983505, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751885341952914, "dur": 47888, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.InputSystem.TestFramework.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751885341952898, "dur": 47906, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.TestFramework.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751885342000817, "dur": 4028, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.InputSystem.TestFramework.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751885342004850, "dur": 909199, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751885342914050, "dur": 1486626, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751885344400676, "dur": 358199, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751885344758876, "dur": 87340, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751885344846216, "dur": 719598, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751885345565815, "dur": 539, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751885345566356, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751885345567116, "dur": 212, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751885345566556, "dur": 773, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751885345567821, "dur": 40257, "ph": "X", "name": "InvokeCacheMe", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.SettingsProvider.Editor.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751885345608085, "dur": 77, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751885345857893, "dur": 37697, "ph": "X", "name": "InvokeCacheMe", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.SettingsProvider.Editor.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751885345609298, "dur": 286302, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751885345897659, "dur": 300, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.SettingsProvider.Editor.ref.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751885345897642, "dur": 321, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.SettingsProvider.Editor.ref.dll_A1A44354E0B583A8.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751885345898003, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.SettingsProvider.Editor.ref.dll_A1A44354E0B583A8.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751885345898057, "dur": 98182, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751885345996240, "dur": 223958, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751885346220199, "dur": 182134, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751885330543948, "dur": 13734, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751885330557685, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_AAC1DDBB583EF4F7.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751885330557884, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_F9300E41473E6FF2.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751885330558001, "dur": 174, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_4DF46BE925392E19.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751885330558339, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_40A52831373DC9B0.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751885330558539, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751885330561045, "dur": 3621, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751885330589205, "dur": 28768, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751885330564671, "dur": 53304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751885330618579, "dur": 82483, "ph": "X", "name": "InvokeCacheMe", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEngine.UI.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751885330701070, "dur": 106, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751885333056669, "dur": 45795, "ph": "X", "name": "InvokeCacheMe", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEngine.UI.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751885330702306, "dur": 2400167, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751885333105219, "dur": 439, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEngine.UI.ref.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751885333105203, "dur": 460, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UI.ref.dll_08FEAA520A2EFD60.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751885333105705, "dur": 883642, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751885333989348, "dur": 365469, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751885334354817, "dur": 446, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751885334355265, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751885334356175, "dur": 711, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751885334355494, "dur": 1393, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751885334357449, "dur": 81887, "ph": "X", "name": "InvokeCacheMe", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751885334439344, "dur": 118, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751885335487450, "dur": 184, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751885335488175, "dur": 420418, "ph": "X", "name": "InvokeCacheMe", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751885334440545, "dur": 1468057, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751885335911308, "dur": 335, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.ref.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751885335911291, "dur": 357, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.ref.dll_E668CD773429A040.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751885335912282, "dur": 247, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751885335911689, "dur": 841, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751885335913059, "dur": 235015, "ph": "X", "name": "InvokeCacheMe", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualStudio.Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751885336148083, "dur": 138, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751885339286407, "dur": 482786, "ph": "X", "name": "InvokeCacheMe", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualStudio.Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751885336149207, "dur": 3619997, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751885339772364, "dur": 313, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualStudio.Editor.ref.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751885339772348, "dur": 334, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualStudio.Editor.ref.dll_3A975DBA53ABA4AD.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751885339772729, "dur": 347, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualStudio.Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751885339772714, "dur": 363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualStudio.Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751885339773093, "dur": 1783, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualStudio.Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751885339774879, "dur": 981775, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751885340756676, "dur": 4071, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Multiplayer.Center.Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751885340756656, "dur": 4092, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751885340760786, "dur": 1140, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Multiplayer.Center.Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751885340761927, "dur": 25272, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751885340787199, "dur": 48574, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751885340835804, "dur": 380, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TextMeshPro.Editor.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1751885340835774, "dur": 411, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.Editor.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1751885340836206, "dur": 1093, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.TextMeshPro.Editor.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1751885340837302, "dur": 68132, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751885340905470, "dur": 209137, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.InputSystem.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751885340905437, "dur": 209172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751885341114643, "dur": 2962, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.InputSystem.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751885341118173, "dur": 239, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.InputSystem.DocCodeSamples.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751885341117612, "dur": 801, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751885341118811, "dur": 111841, "ph": "X", "name": "InvokeCacheMe", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.InputSystem.DocCodeSamples.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751885341230662, "dur": 110, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751885341978096, "dur": 49173, "ph": "X", "name": "InvokeCacheMe", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.InputSystem.DocCodeSamples.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751885341231785, "dur": 795492, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751885342029304, "dur": 287, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.InputSystem.DocCodeSamples.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1751885342029290, "dur": 303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.DocCodeSamples.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1751885342029661, "dur": 1174, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.InputSystem.DocCodeSamples.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1751885342030839, "dur": 883205, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751885342914081, "dur": 16930, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.Core.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751885342914045, "dur": 16968, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751885342931041, "dur": 1607, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.Core.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751885342932655, "dur": 1468023, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751885344400679, "dur": 358195, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751885344758900, "dur": 285, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.State.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1751885344758874, "dur": 312, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1751885344759207, "dur": 837, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.State.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1751885344760048, "dur": 86179, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751885344846228, "dur": 719583, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751885345565844, "dur": 78730, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.Flow.Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751885345565822, "dur": 78754, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751885345644603, "dur": 3526, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.Flow.Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751885345648134, "dur": 249513, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751885345897648, "dur": 98576, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751885345996256, "dur": 58122, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.State.Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751885345996226, "dur": 58154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751885346054407, "dur": 1140, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.State.Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751885346055551, "dur": 164649, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751885346220200, "dur": 182035, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885330543791, "dur": 13867, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885330557665, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_7359E91D15751A95.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751885330557889, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_55EDFAE7741AF365.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751885330557997, "dur": 357, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_2504973AF8D3F8E7.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751885330558356, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_B9448EEF9FF8FB2A.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751885330558979, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751885330560652, "dur": 751, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751885330561557, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885330562536, "dur": 6596, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751885330569800, "dur": 12097, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751885330582900, "dur": 1574, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751885330585670, "dur": 859, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751885330589333, "dur": 455, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751885330590362, "dur": 2517, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751885330592884, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885330593160, "dur": 349, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885330593510, "dur": 303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885330593814, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885330594074, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885330594319, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885330594580, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885330594825, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885330595101, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885330595328, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885330595594, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885330595855, "dur": 298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885330596154, "dur": 276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885330596431, "dur": 424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885330596855, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885330597119, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885330597380, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885330597625, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885330597870, "dur": 298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885330598168, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885330598421, "dur": 318, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885330598739, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885330598972, "dur": 435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885330599407, "dur": 280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885330599688, "dur": 290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885330600048, "dur": 677, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Macros\\Macro.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751885330599979, "dur": 928, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885330600907, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885330601152, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885330601406, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885330601632, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885330601846, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885330602057, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885330602278, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885330602495, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885330602718, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885330602931, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885330603179, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885330603452, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885330603632, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885330603859, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885330604079, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885330604405, "dur": 2484959, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885333089364, "dur": 15839, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885333105231, "dur": 161702, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEngine.UI.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751885333105208, "dur": 161727, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.UI.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751885333266962, "dur": 1328, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEngine.UI.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751885333268298, "dur": 721073, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885333989372, "dur": 365457, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885334354829, "dur": 441, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885334355274, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751885334356232, "dur": 662, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751885334355463, "dur": 1432, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751885334357736, "dur": 65299, "ph": "X", "name": "InvokeCacheMe", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TextMeshPro.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751885334423044, "dur": 130, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885337510862, "dur": 411286, "ph": "X", "name": "InvokeCacheMe", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TextMeshPro.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751885334424403, "dur": 3497754, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751885337925488, "dur": 359, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TextMeshPro.ref.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751885337925473, "dur": 378, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.ref.dll_84A4293DBDD182DB.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751885337925890, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751885337926694, "dur": 257, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751885337926089, "dur": 863, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751885337927526, "dur": 337831, "ph": "X", "name": "InvokeCacheMe", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TextMeshPro.Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751885338265366, "dur": 141, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885340753732, "dur": 78820, "ph": "X", "name": "InvokeCacheMe", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TextMeshPro.Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751885338266588, "dur": 2565975, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751885340835787, "dur": 355, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TextMeshPro.Editor.ref.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751885340835770, "dur": 382, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.Editor.ref.dll_159E061D77A10B86.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751885340836184, "dur": 69264, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885340905449, "dur": 911, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885340906362, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751885340907615, "dur": 247, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751885340906585, "dur": 1278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751885340908335, "dur": 62251, "ph": "X", "name": "InvokeCacheMe", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.InputSystem.ForUI.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751885340970595, "dur": 129, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885341913310, "dur": 84581, "ph": "X", "name": "InvokeCacheMe", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.InputSystem.ForUI.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751885340971710, "dur": 1026219, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751885342000491, "dur": 244, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.InputSystem.ForUI.ref.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751885342000474, "dur": 265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.InputSystem.ForUI.ref.dll_0478B67D1094CE70.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751885342000772, "dur": 913275, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885342914048, "dur": 508, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885342914558, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751885342916148, "dur": 340, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751885342914704, "dur": 1785, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751885342917764, "dur": 36024, "ph": "X", "name": "InvokeCacheMe", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.Core.Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751885342953795, "dur": 235, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885344800441, "dur": 38317, "ph": "X", "name": "InvokeCacheMe", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.Core.Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751885342955283, "dur": 1883491, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751885344846232, "dur": 603, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.Core.Editor.ref.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751885344846213, "dur": 626, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Core.Editor.ref.dll_EDC8690F57C5BDFD.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751885344846881, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751885344848104, "dur": 285, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751885344847115, "dur": 1276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751885344849194, "dur": 30140, "ph": "X", "name": "InvokeCacheMe", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.Flow.Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751885344879341, "dur": 107, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885345468866, "dur": 93618, "ph": "X", "name": "InvokeCacheMe", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.Flow.Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751885344880476, "dur": 682016, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751885345565828, "dur": 480, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.Flow.Editor.ref.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751885345565812, "dur": 501, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Flow.Editor.ref.dll_6AF951BE66A4493E.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751885345566352, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751885345567278, "dur": 237, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751885345566559, "dur": 957, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751885345568137, "dur": 40028, "ph": "X", "name": "InvokeCacheMe", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.State.Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751885345608169, "dur": 93, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885345953587, "dur": 38917, "ph": "X", "name": "InvokeCacheMe", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.State.Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751885345609358, "dur": 383157, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751885345996241, "dur": 366, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.State.Editor.ref.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751885345996222, "dur": 389, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.State.Editor.ref.dll_E03858E727F23F6F.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751885345996647, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751885345997460, "dur": 242, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751885345996861, "dur": 843, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751885345998184, "dur": 37321, "ph": "X", "name": "InvokeCacheMe", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.Shared.Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751885346035512, "dur": 70, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885346186382, "dur": 32157, "ph": "X", "name": "InvokeCacheMe", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.Shared.Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751885346036624, "dur": 181922, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751885346220195, "dur": 389, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.Shared.Editor.ref.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751885346220177, "dur": 410, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Shared.Editor.ref.dll_9669F6EBFF6DFFB7.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751885346220617, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751885346221194, "dur": 202, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751885346220777, "dur": 620, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751885346221823, "dur": 72, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885346222857, "dur": 152474, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751885346378382, "dur": 21993, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751885346378366, "dur": 22011, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751885346400409, "dur": 1733, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751885330543846, "dur": 13824, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885330557676, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_0E44AF698B21F99B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751885330557892, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_676A4B919D1115D9.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751885330558334, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885330558795, "dur": 734, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_A3850CF6800E4193.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751885330559559, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751885330559749, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751885330562131, "dur": 11809, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751885330574693, "dur": 8678, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751885330584139, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751885330585508, "dur": 5312, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751885330592165, "dur": 6882, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751885330599049, "dur": 429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885330599479, "dur": 296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885330600043, "dur": 675, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Reflection\\MemberUtility.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751885330599776, "dur": 942, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885330600719, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885330600990, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885330601252, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885330601467, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885330601731, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885330601943, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885330602173, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885330602389, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885330602617, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885330602836, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885330603064, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885330603310, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885330603531, "dur": 337, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885330603868, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885330604089, "dur": 345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885330604434, "dur": 2484640, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885333089306, "dur": 15909, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885333105215, "dur": 884131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885333989366, "dur": 96463, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEditor.TestRunner.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751885333989347, "dur": 96484, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.TestRunner.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751885334085861, "dur": 1465, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEditor.TestRunner.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751885334087331, "dur": 267491, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885334354822, "dur": 467, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885334355290, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751885334356178, "dur": 1189, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751885334355499, "dur": 1872, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751885334358112, "dur": 58350, "ph": "X", "name": "InvokeCacheMe", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Timeline.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751885334416471, "dur": 143, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885335907415, "dur": 207262, "ph": "X", "name": "InvokeCacheMe", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Timeline.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751885334417574, "dur": 1697113, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751885336118465, "dur": 294, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Timeline.ref.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751885336118449, "dur": 315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Timeline.ref.dll_621CDDF9C514DF8F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751885336118799, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751885336119879, "dur": 223, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751885336118983, "dur": 1121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751885336120886, "dur": 144542, "ph": "X", "name": "InvokeCacheMe", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Timeline.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751885336265437, "dur": 332, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885341445547, "dur": 314546, "ph": "X", "name": "InvokeCacheMe", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Timeline.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751885336266972, "dur": 5493132, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751885341765626, "dur": 389, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Timeline.Editor.ref.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751885341765519, "dur": 502, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Timeline.Editor.ref.dll_0D3D3C0B73557612.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751885341766071, "dur": 52008, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Timeline.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751885341766058, "dur": 52023, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751885341818107, "dur": 1570, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Timeline.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751885341819682, "dur": 180880, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885342000588, "dur": 187, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.InputSystem.ForUI.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1751885342000564, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.ForUI.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1751885342000790, "dur": 1194, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.InputSystem.ForUI.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1751885342001986, "dur": 912076, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885342914062, "dur": 1486635, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885344400697, "dur": 358180, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885344758878, "dur": 87333, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885344846246, "dur": 635, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.Core.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1751885344846218, "dur": 670, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1751885344846909, "dur": 1456, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1751885344848370, "dur": 717451, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885345565821, "dur": 331824, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885345897668, "dur": 296, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.SettingsProvider.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1751885345897646, "dur": 319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.SettingsProvider.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1751885345897978, "dur": 68, "ph": "X", "name": "EmitNodeStart", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885345898049, "dur": 1430, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.SettingsProvider.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1751885345899482, "dur": 96744, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885345996227, "dur": 223961, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885346220189, "dur": 182096, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751885330544021, "dur": 13671, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751885330557695, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_AD3F2B6EFF130063.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751885330557799, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_AD3F2B6EFF130063.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751885330557898, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_86082FB9E11717DE.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751885330557988, "dur": 101, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_86082FB9E11717DE.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751885330558336, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751885330558514, "dur": 304, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751885330562204, "dur": 44745, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751885330606952, "dur": 2481993, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751885333088973, "dur": 308, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEngine.TestRunner.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1751885333088948, "dur": 334, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1751885333089322, "dur": 2910, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEngine.TestRunner.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1751885333092236, "dur": 13029, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751885333105265, "dur": 884101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751885333989367, "dur": 365434, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751885334354826, "dur": 531824, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEditor.UI.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751885334354802, "dur": 531850, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.UI.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751885334886674, "dur": 383, "ph": "X", "name": "EmitNodeStart", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751885334887062, "dur": 2072, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEditor.UI.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751885334889140, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751885334890594, "dur": 314, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751885334889346, "dur": 1564, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751885334891708, "dur": 78674, "ph": "X", "name": "InvokeCacheMe", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.PlasticSCM.Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751885334970388, "dur": 269, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751885340660285, "dur": 120325, "ph": "X", "name": "InvokeCacheMe", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.PlasticSCM.Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751885334971791, "dur": 5808829, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751885340787145, "dur": 16319, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.PlasticSCM.Editor.ref.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751885340787126, "dur": 16343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.PlasticSCM.Editor.ref.dll_257AEB342BE77856.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751885340803518, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751885340804239, "dur": 292, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.CollabProxy.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751885340803693, "dur": 841, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751885340805044, "dur": 74949, "ph": "X", "name": "InvokeCacheMe", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.CollabProxy.Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751885340880002, "dur": 137, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751885341448707, "dur": 327069, "ph": "X", "name": "InvokeCacheMe", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.CollabProxy.Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751885340881137, "dur": 894650, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751885341778551, "dur": 282, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.CollabProxy.Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751885341778537, "dur": 298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.CollabProxy.Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751885341778869, "dur": 1105, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.CollabProxy.Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751885341779978, "dur": 172923, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751885341952924, "dur": 321, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.InputSystem.TestFramework.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1751885341952902, "dur": 344, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.TestFramework.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1751885341953276, "dur": 1001, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.InputSystem.TestFramework.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1751885341954282, "dur": 46194, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751885342000505, "dur": 248, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.InputSystem.ForUI.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751885342000479, "dur": 275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.ForUI.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751885342000776, "dur": 1131, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.InputSystem.ForUI.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751885342001912, "dur": 912134, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751885342914076, "dur": 414, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.Core.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1751885342914047, "dur": 444, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1751885342914514, "dur": 1134, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.Core.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1751885342915653, "dur": 1485035, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751885344400688, "dur": 358184, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751885344758910, "dur": 10543, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.State.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751885344758873, "dur": 10581, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751885344769477, "dur": 1037, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.State.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751885344770518, "dur": 75700, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751885344846218, "dur": 719595, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751885345565845, "dur": 430, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.Flow.Editor.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1751885345565814, "dur": 462, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.Editor.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1751885345566313, "dur": 1187, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.Flow.Editor.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1751885345567503, "dur": 330149, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751885345897653, "dur": 98585, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751885345996238, "dur": 223941, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751885346220211, "dur": 306, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.Shared.Editor.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1751885346220180, "dur": 338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Shared.Editor.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1751885346220548, "dur": 924, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.Shared.Editor.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1751885346221477, "dur": 180724, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751885330544086, "dur": 13630, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751885330558347, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_1D503C7A8290C7BE.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751885330558513, "dur": 308, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_7E2204058CA93257.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751885330558843, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751885330559061, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751885330560790, "dur": 1746, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751885330563153, "dur": 5915, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751885330573938, "dur": 10200, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751885330584179, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751885330585002, "dur": 904, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751885330586454, "dur": 4388, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751885330591575, "dur": 2676, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751885330594253, "dur": 310, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751885330594564, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751885330594826, "dur": 279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751885330595105, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751885330595364, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751885330595623, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751885330595877, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751885330596151, "dur": 286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751885330596438, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751885330596711, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751885330596968, "dur": 304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751885330597272, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751885330597547, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751885330597813, "dur": 286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751885330598100, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751885330598362, "dur": 328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751885330598690, "dur": 371, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751885330599062, "dur": 310, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751885330599372, "dur": 295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751885330599917, "dur": 826, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Reflection\\Optimization\\StaticActionInvoker_0.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751885330599668, "dur": 1099, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751885330600767, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751885330600986, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751885330601232, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751885330601449, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751885330601665, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751885330601869, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751885330602082, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751885330602285, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751885330602486, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751885330602743, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751885330602959, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751885330603207, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751885330603457, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751885330603684, "dur": 312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751885330603997, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751885330604205, "dur": 327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751885330604533, "dur": 2484479, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751885333089013, "dur": 16193, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751885333105227, "dur": 448, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEngine.UI.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751885333105207, "dur": 469, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.UI.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751885333105702, "dur": 1201, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEngine.UI.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751885333106908, "dur": 882435, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751885333989370, "dur": 672, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEditor.TestRunner.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751885333989345, "dur": 698, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.TestRunner.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751885333990064, "dur": 1227, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEditor.TestRunner.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751885333991300, "dur": 363499, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751885334354818, "dur": 388, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEditor.UI.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751885334354800, "dur": 407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.UI.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751885334355233, "dur": 7517, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEditor.UI.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751885334362755, "dur": 842, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751885334364258, "dur": 272, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Settings.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751885334363631, "dur": 900, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751885334365182, "dur": 83623, "ph": "X", "name": "InvokeCacheMe", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Settings.Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751885334448814, "dur": 131, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751885335665323, "dur": 309620, "ph": "X", "name": "InvokeCacheMe", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Settings.Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751885334450101, "dur": 1524852, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751885335977667, "dur": 272, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Settings.Editor.ref.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751885335977653, "dur": 291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Settings.Editor.ref.dll_7F5B154C05A780B0.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751885335977978, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751885335978701, "dur": 218, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TestTools.CodeCoverage.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751885335978159, "dur": 762, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751885335979377, "dur": 182384, "ph": "X", "name": "InvokeCacheMe", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TestTools.CodeCoverage.Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751885336161774, "dur": 143, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751885338128839, "dur": 339566, "ph": "X", "name": "InvokeCacheMe", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TestTools.CodeCoverage.Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751885336162933, "dur": 2305482, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751885338471517, "dur": 339, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TestTools.CodeCoverage.Editor.ref.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751885338471501, "dur": 359, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TestTools.CodeCoverage.Editor.ref.dll_F09CF4826CE11CD9.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751885338471914, "dur": 207, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Rider.Editor.ref.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751885338471900, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Rider.Editor.ref.dll_9B5591808ABA37AF.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751885338472165, "dur": 169, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.EditorCoroutines.Editor.ref.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751885338472151, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.EditorCoroutines.Editor.ref.dll_34ED10A34098B2DB.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751885338472375, "dur": 468, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TextMeshPro.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751885338472360, "dur": 484, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751885338472886, "dur": 1349, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.TextMeshPro.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751885338474255, "dur": 294, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Timeline.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751885338474240, "dur": 310, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751885338474568, "dur": 1114, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Timeline.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751885338475704, "dur": 213, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Multiplayer.Center.Common.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751885338475686, "dur": 232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751885338475937, "dur": 958, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751885338476917, "dur": 226, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TestTools.CodeCoverage.Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751885338476903, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751885338477164, "dur": 1183, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751885338478378, "dur": 328, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TestTools.CodeCoverage.Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751885338478355, "dur": 352, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751885338478728, "dur": 1151, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751885338479902, "dur": 393, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TextMeshPro.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751885338479885, "dur": 412, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751885338480325, "dur": 1166, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.TextMeshPro.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751885338481514, "dur": 261, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Rider.Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751885338481496, "dur": 281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rider.Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751885338481819, "dur": 1031, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Rider.Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751885338482875, "dur": 241, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Rider.Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751885338482855, "dur": 262, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rider.Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751885338483133, "dur": 1116, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Rider.Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751885338484274, "dur": 195, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.EditorCoroutines.Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751885338484254, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.EditorCoroutines.Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751885338484489, "dur": 967, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.EditorCoroutines.Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751885338485482, "dur": 210, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.EditorCoroutines.Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751885338485460, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.EditorCoroutines.Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751885338485717, "dur": 1077, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.EditorCoroutines.Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751885338486817, "dur": 271, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Timeline.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751885338486799, "dur": 290, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751885338487108, "dur": 1101, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Timeline.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751885338488236, "dur": 267, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Settings.Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751885338488219, "dur": 286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Settings.Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751885338488523, "dur": 1122, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Settings.Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751885338489679, "dur": 255, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Settings.Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751885338489658, "dur": 278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Settings.Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751885338489993, "dur": 2257, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Settings.Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751885338492290, "dur": 187, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751885338492270, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751885338492506, "dur": 1000, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751885338493528, "dur": 212, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751885338493511, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751885338493759, "dur": 1227, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751885338495012, "dur": 212, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751885338494991, "dur": 235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751885338495245, "dur": 1217, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751885338496487, "dur": 221, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751885338496467, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751885338496731, "dur": 1062, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751885338497818, "dur": 203, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Multiplayer.Center.Common.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751885338497798, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751885338498047, "dur": 972, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751885338499022, "dur": 1273329, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751885339772371, "dur": 308, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualStudio.Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751885339772352, "dur": 328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualStudio.Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751885339772708, "dur": 1226, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualStudio.Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751885339773938, "dur": 982789, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751885340756749, "dur": 3905, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Multiplayer.Center.Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751885340756728, "dur": 3928, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751885340760688, "dur": 1210, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Multiplayer.Center.Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751885340761901, "dur": 25227, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751885340787150, "dur": 563, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.PlasticSCM.Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751885340787129, "dur": 586, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PlasticSCM.Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751885340787743, "dur": 1565, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.PlasticSCM.Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751885340789313, "dur": 46458, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751885340835804, "dur": 1441562, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TextMeshPro.Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751885340835773, "dur": 1441595, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751885342277395, "dur": 1252, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.TextMeshPro.Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751885342278651, "dur": 635410, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751885342914061, "dur": 1486613, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751885344400700, "dur": 369, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.Flow.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751885344400675, "dur": 395, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751885344401117, "dur": 971, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.Flow.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751885344402093, "dur": 356800, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751885344758894, "dur": 87337, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751885344846231, "dur": 719595, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751885345565826, "dur": 331816, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751885345897674, "dur": 366, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.SettingsProvider.Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751885345897647, "dur": 395, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.SettingsProvider.Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751885345898060, "dur": 1247, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.SettingsProvider.Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751885345899314, "dur": 96931, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751885345996245, "dur": 223932, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751885346220211, "dur": 6201, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.Shared.Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751885346220179, "dur": 6235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Shared.Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751885346226442, "dur": 1093, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.Shared.Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751885346227539, "dur": 174808, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885330544148, "dur": 13610, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885330558333, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_5BB3CA76865503EA.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751885330561026, "dur": 4156, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751885330566117, "dur": 15724, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751885330582493, "dur": 2087, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751885330585675, "dur": 1702, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751885330587944, "dur": 3620, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751885330592088, "dur": 2295, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751885330594384, "dur": 278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885330594662, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885330594892, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885330595153, "dur": 387, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885330595540, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885330595783, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885330596044, "dur": 338, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885330596383, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885330596647, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885330596901, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885330597166, "dur": 280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885330597447, "dur": 277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885330597724, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885330597997, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885330598255, "dur": 289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885330598544, "dur": 320, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885330598865, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885330599105, "dur": 328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885330599433, "dur": 276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885330599710, "dur": 285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885330600043, "dur": 670, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Listeners\\UIInterfaces\\UnityOnBeginDragMessageListener.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751885330599997, "dur": 916, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885330600914, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885330601171, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885330601394, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885330601615, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885330601836, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885330602054, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885330602262, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885330602464, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885330602680, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885330602897, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885330603122, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885330603344, "dur": 139, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885330603483, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885330603718, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885330603956, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885330604169, "dur": 340, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885330604509, "dur": 2484558, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885333089068, "dur": 16140, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885333105208, "dur": 884148, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885333989356, "dur": 365449, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885334354805, "dur": 463, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885334355269, "dur": 315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751885334356374, "dur": 886, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751885334355613, "dur": 1648, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751885334359138, "dur": 79868, "ph": "X", "name": "InvokeCacheMe", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751885334439015, "dur": 132, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885335335310, "dur": 241105, "ph": "X", "name": "InvokeCacheMe", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751885334440259, "dur": 1136167, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751885335579754, "dur": 319, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.ref.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751885335579738, "dur": 339, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.ref.dll_D3B9364D6FB03093.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751885335580687, "dur": 237, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.EditorCoroutines.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751885335580118, "dur": 807, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751885335581343, "dur": 246889, "ph": "X", "name": "InvokeCacheMe", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.EditorCoroutines.Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751885335828241, "dur": 120, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885337370038, "dur": 507372, "ph": "X", "name": "InvokeCacheMe", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.EditorCoroutines.Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751885335829344, "dur": 2048076, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751885337881210, "dur": 344, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Performance.Profile-Analyzer.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751885337880324, "dur": 1232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751885337882102, "dur": 322359, "ph": "X", "name": "InvokeCacheMe", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Performance.Profile-Analyzer.Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751885338204470, "dur": 122, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885341108211, "dur": 197795, "ph": "X", "name": "InvokeCacheMe", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Performance.Profile-Analyzer.Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751885338205817, "dur": 3100200, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751885341309321, "dur": 501, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Performance.Profile-Analyzer.Editor.ref.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751885341309306, "dur": 524, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Performance.Profile-Analyzer.Editor.ref.dll_65A192712782EB2B.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751885341309926, "dur": 258397, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Performance.Profile-Analyzer.Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751885341309907, "dur": 258418, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Performance.Profile-Analyzer.Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751885341568376, "dur": 1288, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Performance.Profile-Analyzer.Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751885341569686, "dur": 311, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Performance.Profile-Analyzer.Editor.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1751885341569669, "dur": 330, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Performance.Profile-Analyzer.Editor.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1751885341570018, "dur": 1075, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Performance.Profile-Analyzer.Editor.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1751885341571098, "dur": 197196, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885341768330, "dur": 513, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Timeline.Editor.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1751885341768298, "dur": 547, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.Editor.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1751885341768871, "dur": 1453, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Timeline.Editor.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1751885341770328, "dur": 8213, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885341778568, "dur": 268, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.CollabProxy.Editor.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1751885341778543, "dur": 295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.CollabProxy.Editor.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1751885341778877, "dur": 1168, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.CollabProxy.Editor.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1751885341780048, "dur": 220664, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885342000712, "dur": 28582, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885342029323, "dur": 265, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.InputSystem.DocCodeSamples.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751885342029295, "dur": 295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.DocCodeSamples.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751885342029654, "dur": 1206, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.InputSystem.DocCodeSamples.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751885342030862, "dur": 883190, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885342914052, "dur": 1486619, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885344400697, "dur": 33650, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.Flow.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751885344400673, "dur": 33676, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751885344434375, "dur": 1446, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.Flow.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751885344435826, "dur": 323054, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885344758881, "dur": 87333, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885344846245, "dur": 117157, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.Core.Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751885344846215, "dur": 117197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751885344963468, "dur": 2433, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751885344965907, "dur": 599909, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885345565817, "dur": 331891, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885345897709, "dur": 98523, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885345996232, "dur": 223948, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885346220181, "dur": 158193, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885346378400, "dur": 490, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1751885346378376, "dur": 516, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1751885346378922, "dur": 1155, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1751885346380082, "dur": 22146, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751885346408508, "dur": 2739, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 35942, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "netcorerun.dll"}}, {"pid": 35942, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-1"}}, {"pid": 35942, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 35942, "tid": 1, "ts": 1751885330103898, "dur": 340519, "ph": "X", "name": "BuildProgram", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1751885330105772, "dur": 97382, "ph": "X", "name": "BuildProgramContextConstructor", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1751885330403003, "dur": 4915, "ph": "X", "name": "OutputData.Write", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1751885330407921, "dur": 36481, "ph": "X", "name": "Backend.Write", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1751885330409080, "dur": 28168, "ph": "X", "name": "JsonToString", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1751885330453214, "dur": 2178, "ph": "X", "name": "", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1751885330452171, "dur": 3949, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1751885329762314, "dur": 55, "ph": "X", "name": "IPC_Client_InitializeAndConnectToParent", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751885329762426, "dur": 4210, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751885329766660, "dur": 1750, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751885329768509, "dur": 84, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1751885329768593, "dur": 495, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751885329770012, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_BC34EA19774048D3.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751885329770815, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_2504973AF8D3F8E7.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751885329771904, "dur": 4629, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_3BACF753C5B41AC5.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751885329777410, "dur": 93, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_6946749C828DF1C9.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751885329778994, "dur": 4100, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751885329783132, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751885329783705, "dur": 79, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751885329783809, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751885329784066, "dur": 89, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751885329784493, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751885329784888, "dur": 78, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751885329785810, "dur": 191, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751885329786099, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751885329786554, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751885329787561, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751885329788068, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.State.ref.dll_D91762B7076BE462.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751885329789321, "dur": 170, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751885329789997, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751885329790427, "dur": 112, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751885329790818, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.SettingsProvider.Editor.ref.dll_A1A44354E0B583A8.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751885329792342, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Performance.Profile-Analyzer.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751885329792698, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751885329794269, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751885329794689, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751885329769124, "dur": 25782, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751885329794921, "dur": 176228, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751885329971150, "dur": 205, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751885329971455, "dur": 84, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751885329971567, "dur": 2184, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1751885329772020, "dur": 23152, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751885329795351, "dur": 467, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_55EDFAE7741AF365.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751885329795834, "dur": 239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_5E3C6CF57067B635.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751885329796185, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_C7DF13E3C14DE501.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751885329796636, "dur": 1065, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_9A715A3879D71476.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751885329797852, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751885329798069, "dur": 279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751885329798348, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751885329798585, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751885329799216, "dur": 743, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\Animation\\AnimationPlayableAssetEditor.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751885329798818, "dur": 1175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751885329799993, "dur": 308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751885329800302, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751885329800568, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751885329800801, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751885329801070, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751885329801301, "dur": 311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751885329801613, "dur": 278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751885329801892, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751885329802165, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751885329802402, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751885329802644, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751885329802908, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751885329803153, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751885329803421, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751885329803656, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751885329803885, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751885329804129, "dur": 563, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Listeners\\MonoBehaviour\\UnityOnParticleCollisionMessageListener.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751885329804109, "dur": 840, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751885329804950, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751885329805181, "dur": 277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751885329805459, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751885329805708, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751885329805952, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751885329806212, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751885329806453, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751885329806703, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751885329806923, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751885329807170, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751885329807432, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751885329807713, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751885329807953, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751885329808215, "dur": 374, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751885329808590, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751885329808835, "dur": 286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751885329809121, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751885329809382, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751885329809605, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751885329809851, "dur": 380, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751885329810231, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751885329810484, "dur": 176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751885329810879, "dur": 306, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751885329811186, "dur": 830, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751885329812021, "dur": 245, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751885329812285, "dur": 8468, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEditor.UI.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1751885329820759, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751885329820869, "dur": 5324, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.Core.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751885329826201, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751885329826334, "dur": 4860, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.Shared.Editor.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1751885329831229, "dur": 3697, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Timeline.Editor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751885329834963, "dur": 3172, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.TextMeshPro.Editor.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1751885329838195, "dur": 2888, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1751885329841163, "dur": 4554, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1751885329845772, "dur": 7382, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.EditorCoroutines.Editor.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1751885329853166, "dur": 250, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751885329853438, "dur": 6551, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.Core.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1751885329859999, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751885329860120, "dur": 4089, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1751885329864216, "dur": 106937, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751885329772147, "dur": 23744, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751885329795927, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751885329796195, "dur": 795, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_A44562DC2280D3AD.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751885329797035, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751885329797442, "dur": 272, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751885329798335, "dur": 845, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\Window\\TimelineWindow_Selection.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751885329798075, "dur": 1143, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751885329799220, "dur": 749, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751885329799989, "dur": 6960, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751885329807081, "dur": 9679, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEngine.UI.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1751885329816763, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751885329816965, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751885329817185, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751885329817357, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751885329817486, "dur": 576, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751885329818063, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751885329818123, "dur": 622, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751885329818900, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751885329818987, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751885329819197, "dur": 512, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751885329819786, "dur": 4660, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.InputSystem.TestFramework.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751885329824488, "dur": 5652, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.InputSystem.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751885329830146, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751885329830351, "dur": 19371, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.CollabProxy.Editor.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751885329849734, "dur": 424, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751885329850178, "dur": 7091, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.PlasticSCM.Editor.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1751885329857286, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751885329857375, "dur": 5439, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1751885329862820, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751885329862909, "dur": 9872, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEditor.UI.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751885329872825, "dur": 98337, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751885329771171, "dur": 23824, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751885329795011, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_2784FA9BFF134207.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751885329795359, "dur": 806, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751885329796166, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_0D411EBC79DB1BBD.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751885329796531, "dur": 2438, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_B1D6EB5640F05713.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751885329798980, "dur": 469, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751885329799474, "dur": 4057, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751885329803609, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751885329803782, "dur": 7249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751885329811146, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751885329811342, "dur": 600, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751885329812046, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751885329812239, "dur": 1441, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751885329813682, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751885329813780, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751885329814011, "dur": 2309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751885329816396, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751885329816584, "dur": 1590, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751885329818250, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751885329818465, "dur": 651, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751885329819118, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751885329819286, "dur": 560, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751885329819926, "dur": 4661, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.State.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751885329824627, "dur": 5095, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1751885329829763, "dur": 3620, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.CollabProxy.Editor.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1751885329833437, "dur": 1588, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.InputSystem.DocCodeSamples.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1751885329835057, "dur": 3601, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.TextMeshPro.Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751885329838665, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751885329838735, "dur": 3613, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Multiplayer.Center.Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751885329842399, "dur": 5115, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.EditorCoroutines.Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751885329847522, "dur": 1530, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751885329849076, "dur": 5953, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Performance.Profile-Analyzer.Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751885329855044, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751885329855133, "dur": 12983, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751885329868123, "dur": 103003, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885329772062, "dur": 23306, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885329795951, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_B9448EEF9FF8FB2A.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751885329796224, "dur": 337, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_6946749C828DF1C9.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751885329796780, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751885329796990, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751885329797103, "dur": 370, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751885329797776, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751885329797914, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751885329798078, "dur": 359, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885329798437, "dur": 338, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885329798776, "dur": 648, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885329799425, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885329799680, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885329799906, "dur": 479, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885329800569, "dur": 3670, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Flow\\Invocations\\MemberInvocationInspector.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751885329804241, "dur": 1014, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Flow\\Invocations\\InvocationInspector.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751885329800386, "dur": 4926, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885329805313, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885329805583, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885329805824, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885329806072, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885329806325, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885329806570, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885329806805, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885329807074, "dur": 7704, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEngine.UI.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751885329814783, "dur": 728, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751885329815553, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751885329815637, "dur": 633, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751885329816271, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885329816354, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751885329816550, "dur": 1281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751885329817832, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885329817889, "dur": 587, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751885329818523, "dur": 681, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751885329819205, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885329819346, "dur": 4979, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Timeline.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751885329824334, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885329824449, "dur": 5153, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.InputSystem.ForUI.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751885329829613, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885329829727, "dur": 3910, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.SettingsProvider.Editor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1751885329833679, "dur": 3792, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.Flow.Editor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1751885329837480, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751885329837569, "dur": 3501, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.InputSystem.ForUI.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1751885329841126, "dur": 6138, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualStudio.Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751885329847310, "dur": 4152, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Rider.Editor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1751885329851515, "dur": 8527, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Settings.Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751885329860117, "dur": 4105, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1751885329864224, "dur": 106924, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885329772117, "dur": 23259, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885329795807, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_82A68248955BDDC4.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751885329795953, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_7882E8644625FF23.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751885329796217, "dur": 335, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_177F4FE55DE2E02C.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751885329796770, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751885329796894, "dur": 657, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751885329798031, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885329798299, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885329798532, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885329798754, "dur": 513, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885329799268, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885329799504, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885329799757, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885329800002, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885329800247, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885329800511, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885329800771, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885329801037, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885329801280, "dur": 293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885329801573, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885329801826, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885329802071, "dur": 318, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885329802389, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885329802632, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885329802887, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885329803145, "dur": 321, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885329803466, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885329803708, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885329804127, "dur": 558, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Listeners\\UIInterfaces\\UnityOnPointerDownMessageListener.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751885329803945, "dur": 801, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885329804747, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885329805000, "dur": 462, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885329805463, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885329805714, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885329805968, "dur": 766, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Runtime\\Evaluation\\RuntimeClipBase.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751885329805968, "dur": 993, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885329806961, "dur": 470, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885329807432, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885329807664, "dur": 573, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885329808238, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885329808482, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885329808731, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885329808988, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885329809242, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885329809494, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885329809741, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885329809970, "dur": 327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885329810298, "dur": 152, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885329810450, "dur": 501, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885329810952, "dur": 162, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885329811203, "dur": 5751, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEditor.TestRunner.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1751885329816957, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751885329817139, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751885329817306, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751885329817475, "dur": 534, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751885329818055, "dur": 515, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751885329818616, "dur": 1192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751885329819916, "dur": 6363, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.Flow.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751885329826286, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885329826363, "dur": 12402, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.State.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751885329838809, "dur": 429, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885329839252, "dur": 20769, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Multiplayer.Center.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1751885329860061, "dur": 2980, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751885329863043, "dur": 107703, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751885329970747, "dur": 356, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751885329771407, "dur": 23709, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751885329795121, "dur": 277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_0E44AF698B21F99B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751885329795816, "dur": 991, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_D8B62AD188B30A07.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751885329796825, "dur": 104, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_D8B62AD188B30A07.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751885329797077, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751885329797460, "dur": 512, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751885329798065, "dur": 353, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751885329798418, "dur": 338, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751885329798757, "dur": 570, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751885329799327, "dur": 399, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751885329799727, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751885329799960, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751885329800232, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751885329800485, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751885329800740, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751885329800982, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751885329801238, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751885329801478, "dur": 293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751885329801772, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751885329802012, "dur": 598, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751885329802610, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751885329802854, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751885329803127, "dur": 294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751885329803422, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751885329803663, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751885329803881, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751885329804128, "dur": 2502, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Listeners\\UIInterfaces\\UnityOnDeselectMessageListener.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751885329804105, "dur": 2748, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751885329806854, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751885329807081, "dur": 352, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751885329807434, "dur": 289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751885329807723, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751885329807981, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751885329808234, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751885329808475, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751885329808712, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751885329808983, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751885329809249, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751885329809495, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751885329809741, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751885329809978, "dur": 366, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751885329810410, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751885329810875, "dur": 241, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751885329811207, "dur": 13076, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEditor.TestRunner.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751885329824366, "dur": 3712, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751885329828089, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751885329828177, "dur": 3342, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.SettingsProvider.Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751885329831562, "dur": 1851, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.InputSystem.DocCodeSamples.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751885329833454, "dur": 1690, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.InputSystem.TestFramework.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1751885329835152, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751885329835281, "dur": 3629, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.State.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1751885329838919, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751885329838991, "dur": 3322, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualStudio.Editor.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1751885329842351, "dur": 3595, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.Flow.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1751885329845998, "dur": 5381, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Rider.Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751885329851389, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751885329851460, "dur": 17914, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.PlasticSCM.Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751885329869379, "dur": 101751, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751885329771234, "dur": 23780, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751885329795020, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_7359E91D15751A95.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751885329795794, "dur": 526, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_FB065B49AB03584B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751885329796333, "dur": 234, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751885329796744, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751885329796836, "dur": 650, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751885329797866, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751885329797929, "dur": 755, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751885329798686, "dur": 610, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751885329799297, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751885329799520, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751885329799783, "dur": 653, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751885329800568, "dur": 1007, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Flow\\Framework\\Time\\WaitForFlowDescriptor.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751885329800437, "dur": 1269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751885329801706, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751885329801956, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751885329802226, "dur": 362, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751885329802588, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751885329802868, "dur": 563, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Core\\Inspection\\Unity\\Ray2DInspector.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751885329802825, "dur": 819, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751885329804703, "dur": 9570, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEngine.TestRunner.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751885329814277, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751885329814467, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751885329814677, "dur": 735, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751885329815413, "dur": 640, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751885329816066, "dur": 248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751885329816340, "dur": 512, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751885329816929, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751885329817136, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751885329817363, "dur": 627, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751885329818038, "dur": 560, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751885329818599, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751885329818837, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751885329818914, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751885329819091, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751885329819299, "dur": 501, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751885329819846, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751885329820040, "dur": 586, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751885329820627, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751885329820715, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751885329820875, "dur": 6302, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.TextMeshPro.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751885329827195, "dur": 2635, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751885329829905, "dur": 32884, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.State.Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751885329862798, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751885329862865, "dur": 3887, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.InputSystem.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751885329866756, "dur": 104392, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885329771964, "dur": 23184, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885329795345, "dur": 475, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_F9300E41473E6FF2.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751885329795932, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885329796177, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_7FF6DD7231CA2BE3.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751885329796518, "dur": 131, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_E43C745D5056CF76.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751885329796759, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751885329796829, "dur": 170, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751885329797281, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1751885329797664, "dur": 1300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1751885329798965, "dur": 297, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885329799269, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885329799538, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885329799939, "dur": 646, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.State\\Transitions\\NesterStateTransitionWidget.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751885329799792, "dur": 892, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885329800684, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885329800932, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885329801197, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885329801434, "dur": 306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885329801741, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885329801996, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885329802252, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885329802493, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885329802753, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885329803009, "dur": 396, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885329803406, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885329805992, "dur": 8240, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEngine.TestRunner.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1751885329814237, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751885329814434, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751885329814602, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751885329814796, "dur": 598, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751885329815396, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885329815511, "dur": 555, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751885329816257, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751885329816432, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751885329816644, "dur": 1704, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751885329818349, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885329818559, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751885329818791, "dur": 893, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751885329819762, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751885329819955, "dur": 689, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751885329820645, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885329820732, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751885329820925, "dur": 557, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751885329821565, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751885329821809, "dur": 528, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751885329822424, "dur": 3503, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751885329825942, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885329826013, "dur": 4959, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.Shared.Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751885329830983, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885329831080, "dur": 3555, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Timeline.Editor.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1751885329834643, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885329834722, "dur": 3133, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.Flow.Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751885329837958, "dur": 4248, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751885329842257, "dur": 5950, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751885329848252, "dur": 3627, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Performance.Profile-Analyzer.Editor.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1751885329851885, "dur": 384, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751885329852283, "dur": 7714, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Settings.Editor.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1751885329860047, "dur": 2735, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.TextMeshPro.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1751885329862858, "dur": 3263, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Timeline.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1751885329866127, "dur": 104997, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751885329977808, "dur": 1632, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 20696, "tid": 18, "ts": 1751885346436275, "dur": 2484, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend2.traceevents"}}, {"pid": 20696, "tid": 18, "ts": 1751885346441191, "dur": 41, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "buildprogram0.traceevents"}}, {"pid": 20696, "tid": 18, "ts": 1751885346441445, "dur": 22, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 20696, "tid": 18, "ts": 1751885346438793, "dur": 2392, "ph": "X", "name": "backend2.traceevents", "args": {}}, {"pid": 20696, "tid": 18, "ts": 1751885346441256, "dur": 188, "ph": "X", "name": "buildprogram0.traceevents", "args": {}}, {"pid": 20696, "tid": 18, "ts": 1751885346441486, "dur": 453, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 20696, "tid": 18, "ts": 1751885346429893, "dur": 13230, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}