{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 20696, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 20696, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 20696, "tid": 172, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 20696, "tid": 172, "ts": 1751886146431612, "dur": 1553, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 20696, "tid": 172, "ts": 1751886146441378, "dur": 1879, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 20696, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 20696, "tid": 1, "ts": 1751886143537423, "dur": 10407, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 20696, "tid": 1, "ts": 1751886143547841, "dur": 99811, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 20696, "tid": 1, "ts": 1751886143647669, "dur": 66910, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 20696, "tid": 172, "ts": 1751886146443264, "dur": 24, "ph": "X", "name": "", "args": {}}, {"pid": 20696, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143532889, "dur": 18745, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143551639, "dur": 2862481, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143554045, "dur": 5728, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143559787, "dur": 5488, "ph": "X", "name": "ProcessMessages 20496", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143565361, "dur": 2176, "ph": "X", "name": "ReadAsync 20496", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143567549, "dur": 41, "ph": "X", "name": "ProcessMessages 20525", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143567593, "dur": 477, "ph": "X", "name": "ReadAsync 20525", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143568076, "dur": 18, "ph": "X", "name": "ProcessMessages 8563", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143568096, "dur": 78, "ph": "X", "name": "ReadAsync 8563", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143568181, "dur": 3, "ph": "X", "name": "ProcessMessages 679", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143568187, "dur": 64, "ph": "X", "name": "ReadAsync 679", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143568255, "dur": 1, "ph": "X", "name": "ProcessMessages 178", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143568259, "dur": 74, "ph": "X", "name": "ReadAsync 178", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143568339, "dur": 2, "ph": "X", "name": "ProcessMessages 612", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143568344, "dur": 62, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143568411, "dur": 2, "ph": "X", "name": "ProcessMessages 420", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143568416, "dur": 52, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143568536, "dur": 2, "ph": "X", "name": "ProcessMessages 242", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143568540, "dur": 75, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143568620, "dur": 2, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143568624, "dur": 78, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143568708, "dur": 2, "ph": "X", "name": "ProcessMessages 608", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143568712, "dur": 143, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143568860, "dur": 3, "ph": "X", "name": "ProcessMessages 830", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143568866, "dur": 84, "ph": "X", "name": "ReadAsync 830", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143568955, "dur": 2, "ph": "X", "name": "ProcessMessages 486", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143568959, "dur": 51, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143569017, "dur": 2, "ph": "X", "name": "ProcessMessages 69", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143569021, "dur": 69, "ph": "X", "name": "ReadAsync 69", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143569095, "dur": 2, "ph": "X", "name": "ProcessMessages 775", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143569100, "dur": 54, "ph": "X", "name": "ReadAsync 775", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143569162, "dur": 2, "ph": "X", "name": "ProcessMessages 424", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143569167, "dur": 68, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143569241, "dur": 2, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143569246, "dur": 56, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143569307, "dur": 2, "ph": "X", "name": "ProcessMessages 211", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143569312, "dur": 68, "ph": "X", "name": "ReadAsync 211", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143569437, "dur": 2, "ph": "X", "name": "ProcessMessages 482", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143569441, "dur": 68, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143569516, "dur": 3, "ph": "X", "name": "ProcessMessages 597", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143569577, "dur": 89, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143569672, "dur": 3, "ph": "X", "name": "ProcessMessages 1268", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143569677, "dur": 121, "ph": "X", "name": "ReadAsync 1268", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143569805, "dur": 3, "ph": "X", "name": "ProcessMessages 1032", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143569810, "dur": 67, "ph": "X", "name": "ReadAsync 1032", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143569883, "dur": 2, "ph": "X", "name": "ProcessMessages 119", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143569887, "dur": 80, "ph": "X", "name": "ReadAsync 119", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143569972, "dur": 2, "ph": "X", "name": "ProcessMessages 592", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143569976, "dur": 70, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143570053, "dur": 3, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143570059, "dur": 212, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143570276, "dur": 2, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143570281, "dur": 82, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143570368, "dur": 4, "ph": "X", "name": "ProcessMessages 1089", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143570374, "dur": 61, "ph": "X", "name": "ReadAsync 1089", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143570441, "dur": 2, "ph": "X", "name": "ProcessMessages 324", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143570446, "dur": 172, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143570624, "dur": 3, "ph": "X", "name": "ProcessMessages 975", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143570630, "dur": 66, "ph": "X", "name": "ReadAsync 975", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143570703, "dur": 2, "ph": "X", "name": "ProcessMessages 423", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143570708, "dur": 71, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143570785, "dur": 2, "ph": "X", "name": "ProcessMessages 468", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143570789, "dur": 171, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143570966, "dur": 3, "ph": "X", "name": "ProcessMessages 883", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143570971, "dur": 59, "ph": "X", "name": "ReadAsync 883", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143571037, "dur": 2, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143571042, "dur": 57, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143571104, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143571108, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143571174, "dur": 2, "ph": "X", "name": "ProcessMessages 521", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143571178, "dur": 154, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143571337, "dur": 2, "ph": "X", "name": "ProcessMessages 404", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143571341, "dur": 80, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143571426, "dur": 3, "ph": "X", "name": "ProcessMessages 1109", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143571432, "dur": 61, "ph": "X", "name": "ReadAsync 1109", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143571500, "dur": 2, "ph": "X", "name": "ProcessMessages 187", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143571504, "dur": 399, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143571911, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143571915, "dur": 219, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143572142, "dur": 3, "ph": "X", "name": "ProcessMessages 634", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143572147, "dur": 92, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143572245, "dur": 14, "ph": "X", "name": "ProcessMessages 1447", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143572261, "dur": 174, "ph": "X", "name": "ReadAsync 1447", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143572442, "dur": 3, "ph": "X", "name": "ProcessMessages 688", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143572447, "dur": 106, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143572558, "dur": 3, "ph": "X", "name": "ProcessMessages 1465", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143572564, "dur": 54, "ph": "X", "name": "ReadAsync 1465", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143572624, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143572628, "dur": 76, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143572710, "dur": 2, "ph": "X", "name": "ProcessMessages 518", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143572713, "dur": 62, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143572781, "dur": 2, "ph": "X", "name": "ProcessMessages 134", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143572786, "dur": 147, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143572939, "dur": 1, "ph": "X", "name": "ProcessMessages 247", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143572942, "dur": 85, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143573033, "dur": 4, "ph": "X", "name": "ProcessMessages 1214", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143573040, "dur": 75, "ph": "X", "name": "ReadAsync 1214", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143573119, "dur": 2, "ph": "X", "name": "ProcessMessages 546", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143573196, "dur": 152, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143573352, "dur": 4, "ph": "X", "name": "ProcessMessages 1707", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143573359, "dur": 71, "ph": "X", "name": "ReadAsync 1707", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143573436, "dur": 2, "ph": "X", "name": "ProcessMessages 594", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143573440, "dur": 66, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143573512, "dur": 2, "ph": "X", "name": "ProcessMessages 203", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143573516, "dur": 145, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143573667, "dur": 3, "ph": "X", "name": "ProcessMessages 990", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143573679, "dur": 120, "ph": "X", "name": "ReadAsync 990", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143573873, "dur": 63, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143573940, "dur": 92, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143574090, "dur": 4, "ph": "X", "name": "ProcessMessages 1150", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143574222, "dur": 91, "ph": "X", "name": "ReadAsync 1150", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143574376, "dur": 4, "ph": "X", "name": "ProcessMessages 1538", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143574469, "dur": 101, "ph": "X", "name": "ReadAsync 1538", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143574577, "dur": 4, "ph": "X", "name": "ProcessMessages 1350", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143574583, "dur": 107, "ph": "X", "name": "ReadAsync 1350", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143574697, "dur": 2, "ph": "X", "name": "ProcessMessages 188", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143574700, "dur": 85, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143574791, "dur": 2, "ph": "X", "name": "ProcessMessages 1089", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143574795, "dur": 30, "ph": "X", "name": "ReadAsync 1089", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143574828, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143574830, "dur": 61, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143574956, "dur": 1, "ph": "X", "name": "ProcessMessages 130", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143574961, "dur": 89, "ph": "X", "name": "ReadAsync 130", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143575054, "dur": 3, "ph": "X", "name": "ProcessMessages 1154", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143575060, "dur": 134, "ph": "X", "name": "ReadAsync 1154", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143575199, "dur": 2, "ph": "X", "name": "ProcessMessages 638", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143575203, "dur": 66, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143575274, "dur": 1, "ph": "X", "name": "ProcessMessages 442", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143575277, "dur": 65, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143575347, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143575350, "dur": 182, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143575538, "dur": 3, "ph": "X", "name": "ProcessMessages 287", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143575544, "dur": 145, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143575695, "dur": 3, "ph": "X", "name": "ProcessMessages 1308", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143575700, "dur": 87, "ph": "X", "name": "ReadAsync 1308", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143575792, "dur": 5, "ph": "X", "name": "ProcessMessages 1157", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143575799, "dur": 64, "ph": "X", "name": "ReadAsync 1157", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143575868, "dur": 2, "ph": "X", "name": "ProcessMessages 82", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143575872, "dur": 73, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143575950, "dur": 2, "ph": "X", "name": "ProcessMessages 344", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143575954, "dur": 73, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143576034, "dur": 2, "ph": "X", "name": "ProcessMessages 592", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143576039, "dur": 85, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143576129, "dur": 2, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143576132, "dur": 122, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143576260, "dur": 4, "ph": "X", "name": "ProcessMessages 633", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143576266, "dur": 68, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143576339, "dur": 2, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143576343, "dur": 122, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143576471, "dur": 3, "ph": "X", "name": "ProcessMessages 697", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143576477, "dur": 83, "ph": "X", "name": "ReadAsync 697", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143576566, "dur": 2, "ph": "X", "name": "ProcessMessages 470", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143576571, "dur": 70, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143576645, "dur": 2, "ph": "X", "name": "ProcessMessages 341", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143576723, "dur": 84, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143576813, "dur": 2, "ph": "X", "name": "ProcessMessages 760", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143576817, "dur": 80, "ph": "X", "name": "ReadAsync 760", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143576904, "dur": 2, "ph": "X", "name": "ProcessMessages 618", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143576909, "dur": 73, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143577039, "dur": 3, "ph": "X", "name": "ProcessMessages 559", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143577044, "dur": 76, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143577256, "dur": 3, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143577263, "dur": 83, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143577351, "dur": 3, "ph": "X", "name": "ProcessMessages 1936", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143577356, "dur": 234, "ph": "X", "name": "ReadAsync 1936", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143577597, "dur": 2, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143577602, "dur": 81, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143577815, "dur": 5, "ph": "X", "name": "ProcessMessages 1622", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143577824, "dur": 150, "ph": "X", "name": "ReadAsync 1622", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143577978, "dur": 4, "ph": "X", "name": "ProcessMessages 1124", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143577985, "dur": 185, "ph": "X", "name": "ReadAsync 1124", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143578224, "dur": 4, "ph": "X", "name": "ProcessMessages 873", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143578230, "dur": 289, "ph": "X", "name": "ReadAsync 873", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143578525, "dur": 7, "ph": "X", "name": "ProcessMessages 1824", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143578535, "dur": 70, "ph": "X", "name": "ReadAsync 1824", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143578731, "dur": 3, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143578736, "dur": 92, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143578835, "dur": 5, "ph": "X", "name": "ProcessMessages 1323", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143578842, "dur": 62, "ph": "X", "name": "ReadAsync 1323", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143578910, "dur": 94, "ph": "X", "name": "ProcessMessages 358", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143579007, "dur": 89, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143579102, "dur": 3, "ph": "X", "name": "ProcessMessages 1261", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143579108, "dur": 72, "ph": "X", "name": "ReadAsync 1261", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143579186, "dur": 2, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143579191, "dur": 181, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143579379, "dur": 4, "ph": "X", "name": "ProcessMessages 1000", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143579385, "dur": 66, "ph": "X", "name": "ReadAsync 1000", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143579541, "dur": 2, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143579551, "dur": 157, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143579715, "dur": 4, "ph": "X", "name": "ProcessMessages 909", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143579721, "dur": 78, "ph": "X", "name": "ReadAsync 909", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143587063, "dur": 8, "ph": "X", "name": "ProcessMessages 562", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143587074, "dur": 731, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143587814, "dur": 668, "ph": "X", "name": "ProcessMessages 17769", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143588489, "dur": 3673, "ph": "X", "name": "ReadAsync 17769", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143592173, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143592179, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143592252, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143592258, "dur": 101, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143592365, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143592369, "dur": 104, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143592480, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143592486, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143592544, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143592548, "dur": 1267, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143593822, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143593825, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143593888, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143593893, "dur": 53, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143593952, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143593956, "dur": 2928, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143596895, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143596901, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143596974, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143596980, "dur": 6800, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143603792, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143603799, "dur": 96, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143603902, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143603907, "dur": 55, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143603970, "dur": 4, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143603976, "dur": 125, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143604150, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143604155, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143604218, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143604223, "dur": 740, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143604969, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143604973, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143605029, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143605035, "dur": 253, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143605295, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143605299, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143605352, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143605356, "dur": 48, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143605409, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143605412, "dur": 66, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143605484, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143605489, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143605537, "dur": 5, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143605546, "dur": 162, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143605714, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143605720, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143605775, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143605778, "dur": 47, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143605832, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143605836, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143605887, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143605891, "dur": 243, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143606140, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143606144, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143606193, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143606197, "dur": 425, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143606627, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143606631, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143606686, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143606690, "dur": 45, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143606741, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143606744, "dur": 180, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143606931, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143606935, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143606988, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143606992, "dur": 134, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143607132, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143607135, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143607208, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143607212, "dur": 57, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143607275, "dur": 4, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143607281, "dur": 50, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143607336, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143607340, "dur": 45, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143607390, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143607394, "dur": 76, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143607476, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143607480, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143607529, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143607533, "dur": 72, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143607610, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143607613, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143607665, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143607670, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143607725, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143607729, "dur": 266, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143608000, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143608004, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143608051, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143608055, "dur": 373, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143608433, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143608436, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143608488, "dur": 4, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143608494, "dur": 190, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143608689, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143608693, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143608755, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143608774, "dur": 104, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143608882, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143608886, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143608932, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143608936, "dur": 235, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143609177, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143609181, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143609250, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143609255, "dur": 489, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143609750, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143609755, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143609818, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143609822, "dur": 50, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143609877, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143609882, "dur": 46, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143609933, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143609937, "dur": 50, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143609993, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143609997, "dur": 84, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143610086, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143610089, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143610142, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143610147, "dur": 78, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143610230, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143610235, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143610292, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143610297, "dur": 54, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143610357, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143610361, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143610409, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143610412, "dur": 181, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143610599, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143610602, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143610656, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143610661, "dur": 77, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143610744, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143610748, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143610819, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143610825, "dur": 55, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143610886, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143610890, "dur": 71, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143610966, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143610970, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143611022, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143611026, "dur": 53, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143611086, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143611091, "dur": 357, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143611454, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143611458, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143611516, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143611520, "dur": 176, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143611704, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143611708, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143611745, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143611748, "dur": 48, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143611803, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143611808, "dur": 55, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143611870, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143611874, "dur": 54, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143611936, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143611941, "dur": 50, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143611998, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143612002, "dur": 49, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143612055, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143612060, "dur": 50, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143612115, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143612119, "dur": 47, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143612173, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143612177, "dur": 51, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143612235, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143612239, "dur": 54, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143612299, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143612304, "dur": 50, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143612360, "dur": 2, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143612364, "dur": 51, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143612421, "dur": 3, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143612427, "dur": 51, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143612494, "dur": 2, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143612498, "dur": 46, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143612550, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143612555, "dur": 50, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143612611, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143612615, "dur": 63, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143612685, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143612690, "dur": 54, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143612752, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143612756, "dur": 56, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143612818, "dur": 2, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143612823, "dur": 52, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143612880, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143612885, "dur": 64, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143612955, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143612959, "dur": 48, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143613013, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143613017, "dur": 54, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143613078, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143613082, "dur": 50, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143613138, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143613141, "dur": 1281, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143614430, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143614435, "dur": 71, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143614513, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143614517, "dur": 53, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143614576, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143614580, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143614635, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143614639, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143614694, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143614698, "dur": 56, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143614762, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143614767, "dur": 56, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143614828, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143614832, "dur": 762, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143615601, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143615605, "dur": 86, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143615698, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143615704, "dur": 69, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143615786, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143615791, "dur": 71, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143615870, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143615875, "dur": 225, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143616106, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143616111, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143616166, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143616171, "dur": 1182, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143617360, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143617365, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143617426, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143617431, "dur": 62, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143617501, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143617505, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143617570, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143617575, "dur": 309, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143617890, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143617894, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143617950, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143617955, "dur": 54, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143618015, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143618021, "dur": 729, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143618758, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143618762, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143618825, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143618829, "dur": 53, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143618889, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143618894, "dur": 123, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143619023, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143619031, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143619092, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143619097, "dur": 51, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143619155, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143619160, "dur": 54, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143619220, "dur": 13, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143619237, "dur": 370, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143619614, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143619618, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143619676, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143619680, "dur": 635, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143620322, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143620326, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143620389, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143620393, "dur": 45, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143620441, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143620444, "dur": 63, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143620514, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143620518, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143620572, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143620576, "dur": 200, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143620785, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143620789, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143620846, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143620851, "dur": 1997, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143622871, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143622876, "dur": 71, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143622956, "dur": 1060, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886143624022, "dur": 2732353, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886146356387, "dur": 6, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886146356396, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886146356469, "dur": 2, "ph": "X", "name": "ProcessMessages 212", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886146356489, "dur": 81, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886146356576, "dur": 3295, "ph": "X", "name": "ProcessMessages 107", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886146359881, "dur": 748, "ph": "X", "name": "ReadAsync 107", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886146360638, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886146360644, "dur": 73, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886146360723, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886146360729, "dur": 4701, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886146365447, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886146365453, "dur": 1509, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886146366969, "dur": 45, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886146367018, "dur": 30827, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886146397855, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886146397862, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886146397927, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886146397933, "dur": 2093, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886146400037, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886146400043, "dur": 328, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886146400379, "dur": 32, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886146400414, "dur": 280, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886146400701, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886146400705, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886146400767, "dur": 616, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886146401390, "dur": 12448, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 20696, "tid": 172, "ts": 1751886146443291, "dur": 1986, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 20696, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 20696, "tid": 8589934592, "ts": 1751886143519015, "dur": 195616, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 20696, "tid": 8589934592, "ts": 1751886143714635, "dur": 9, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 20696, "tid": 8589934592, "ts": 1751886143714646, "dur": 2684, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 20696, "tid": 172, "ts": 1751886146445282, "dur": 14, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 20696, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 20696, "tid": 4294967296, "ts": 1751886143480034, "dur": 2936495, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 20696, "tid": 4294967296, "ts": 1751886143489003, "dur": 16526, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 20696, "tid": 4294967296, "ts": 1751886146416883, "dur": 9226, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 20696, "tid": 4294967296, "ts": 1751886146422509, "dur": 224, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 20696, "tid": 4294967296, "ts": 1751886146426281, "dur": 28, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 20696, "tid": 172, "ts": 1751886146445299, "dur": 19, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1751886143546030, "dur": 118, "ph": "X", "name": "IPC_Client_InitializeAndConnectToParent", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751886143546225, "dur": 4416, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751886143550669, "dur": 860, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751886143551636, "dur": 87, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1751886143551724, "dur": 568, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751886143553175, "dur": 136, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_78F0F79A0FB195D1.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751886143555110, "dur": 4123, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_3BACF753C5B41AC5.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751886143561657, "dur": 5386, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751886143568550, "dur": 296, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751886143568999, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Common.ref.dll_EE36537354EA42C8.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751886143569150, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751886143569252, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751886143569501, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751886143569677, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751886143569757, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751886143569909, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751886143570014, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751886143570071, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751886143570211, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751886143570356, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751886143570464, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751886143570615, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751886143570803, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751886143570889, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751886143570985, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751886143571165, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751886143571282, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751886143571524, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751886143571598, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751886143571764, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751886143571997, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751886143572072, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751886143572240, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751886143572829, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751886143573172, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751886143573339, "dur": 83, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751886143573514, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751886143573674, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751886143573929, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751886143574245, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751886143574340, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751886143574470, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751886143574671, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.InputSystem.ForUI.ref.dll_0478B67D1094CE70.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751886143574828, "dur": 79, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751886143575103, "dur": 94, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751886143575373, "dur": 81, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751886143575584, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751886143576026, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751886143576098, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751886143576161, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751886143576259, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751886143576433, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751886143576591, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751886143576772, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751886143576840, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751886143576932, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751886143577159, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751886143577299, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751886143577372, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751886143577632, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751886143577713, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751886143577810, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751886143577951, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Shared.Editor.ref.dll_9669F6EBFF6DFFB7.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751886143578198, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751886143578308, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualStudio.Editor.ref.dll_3A975DBA53ABA4AD.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751886143578491, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751886143578726, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.TestRunner.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751886143578890, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualStudio.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751886143579172, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751886143579245, "dur": 154, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751886143579439, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751886143579637, "dur": 83, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Settings.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751886143579727, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Settings.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751886143579899, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751886143580183, "dur": 93, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751886143580283, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751886143580460, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751886143580630, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.DocCodeSamples.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751886143581268, "dur": 171, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751886143552343, "dur": 30149, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751886143582511, "dur": 2818512, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751886146401024, "dur": 219, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751886146401243, "dur": 78, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751886146401456, "dur": 72, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751886146401553, "dur": 2478, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1751886143552854, "dur": 29757, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751886143582621, "dur": 415, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_AAC1DDBB583EF4F7.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751886143583373, "dur": 150, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_9923BABDF0F3077F.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751886143583714, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_1D503C7A8290C7BE.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751886143584550, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751886143584868, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751886143585000, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751886143585332, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751886143585413, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751886143585673, "dur": 335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751886143586009, "dur": 317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751886143586327, "dur": 344, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751886143586671, "dur": 695, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751886143587532, "dur": 7238, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\Extensions\\AnimatedParameterExtensions.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751886143587366, "dur": 7645, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751886143595012, "dur": 383, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751886143595396, "dur": 308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751886143595925, "dur": 916, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Reflection\\Optimization\\InstanceFunctionInvoker_1.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751886143595705, "dur": 1297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751886143597003, "dur": 395, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751886143597399, "dur": 388, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751886143597972, "dur": 4256, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Runtime\\GroupTrack.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751886143597787, "dur": 4652, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751886143602439, "dur": 315, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751886143602754, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751886143603443, "dur": 741, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework\\UnityEditor.TestRunner\\TestRun\\Tasks\\Scene\\CreateNewSceneTask.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751886143603018, "dur": 1168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751886143604186, "dur": 502, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751886143604746, "dur": 1168, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751886143605916, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751886143606136, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751886143606262, "dur": 279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751886143606605, "dur": 4189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751886143610795, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751886143610979, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751886143611103, "dur": 323, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751886143611467, "dur": 1186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751886143612655, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751886143613020, "dur": 229, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Performance.Profile-Analyzer.Editor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751886143613251, "dur": 247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Editor.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1751886143613499, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751886143613644, "dur": 118, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751886143613762, "dur": 1767, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751886143615529, "dur": 154, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751886143615683, "dur": 991, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751886143616675, "dur": 1654, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751886143618330, "dur": 1310, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751886143619641, "dur": 296, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751886143619977, "dur": 1287, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751886143621265, "dur": 96958, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751886143719623, "dur": 457, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "E:/Unity/Editior/6000.0.34f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 1, "ts": 1751886143718225, "dur": 1865, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751886143720091, "dur": 2680887, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886143553532, "dur": 29118, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886143582654, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_31DB0EF841A9843D.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751886143582923, "dur": 477, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_86082FB9E11717DE.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751886143583416, "dur": 320, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_86082FB9E11717DE.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751886143583738, "dur": 1095, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_7882E8644625FF23.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751886143584975, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886143585241, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751886143585904, "dur": 1534, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.visualstudio\\Editor\\VisualStudioForMacInstallation.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751886143585638, "dur": 1893, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886143587554, "dur": 1271, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\Audio\\AudioPlayableAssetEditor.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751886143588998, "dur": 2504, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\Animation\\BindingTreeViewDataSourceGUI.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751886143587531, "dur": 4080, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886143591612, "dur": 311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886143591995, "dur": 1133, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Flow\\Framework\\Logic\\NotEqual.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751886143591923, "dur": 1560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886143593484, "dur": 320, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886143593804, "dur": 313, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886143594118, "dur": 382, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886143594500, "dur": 318, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886143594819, "dur": 297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886143595117, "dur": 348, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886143595466, "dur": 324, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886143595913, "dur": 1294, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Reflection\\Operators\\GreaterThanHandler.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751886143595790, "dur": 1604, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886143597395, "dur": 310, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886143598042, "dur": 1158, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Runtime\\Utilities\\WeightUtility.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751886143597706, "dur": 1606, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886143599313, "dur": 301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886143599614, "dur": 346, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886143599961, "dur": 296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886143600258, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886143600546, "dur": 331, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886143600878, "dur": 294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886143601172, "dur": 279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886143601451, "dur": 291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886143601743, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886143602207, "dur": 1786, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy\\Editor\\PlasticPluginIsEnabledPreference.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751886143602015, "dur": 2069, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886143604084, "dur": 557, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886143604648, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886143604775, "dur": 1874, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886143606650, "dur": 733, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751886143607384, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886143607554, "dur": 264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751886143607843, "dur": 249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751886143608093, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886143608189, "dur": 2718, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751886143610955, "dur": 1734, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751886143612690, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886143612806, "dur": 276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751886143613119, "dur": 2372, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751886143615492, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886143615625, "dur": 922, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Core.Editor.ref.dll_EDC8690F57C5BDFD.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751886143616552, "dur": 366, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751886143616964, "dur": 1168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751886143618134, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886143618275, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886143618346, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Flow.Editor.ref.dll_6AF951BE66A4493E.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751886143618452, "dur": 235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751886143618725, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751886143618818, "dur": 994, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751886143619813, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886143619949, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886143620038, "dur": 395, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751886143620469, "dur": 627, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751886143621105, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886143621403, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751886143621637, "dur": 478, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751886143622610, "dur": 91, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886143625422, "dur": 2731983, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751886146360738, "dur": 37890, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751886146360717, "dur": 37914, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751886146398673, "dur": 2198, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751886143553486, "dur": 29142, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751886143582633, "dur": 800, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_AD3F2B6EFF130063.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751886143583447, "dur": 310, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_AD3F2B6EFF130063.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751886143583759, "dur": 1298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_7FF6DD7231CA2BE3.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751886143585122, "dur": 630, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751886143585779, "dur": 7159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751886143593046, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751886143593338, "dur": 11198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751886143604538, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751886143604679, "dur": 247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751886143604968, "dur": 786, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751886143605755, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751886143605881, "dur": 238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751886143606158, "dur": 1912, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751886143608071, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751886143608205, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751886143608480, "dur": 3913, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751886143612394, "dur": 333, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751886143612773, "dur": 271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751886143613069, "dur": 2145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751886143615216, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751886143615335, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751886143615568, "dur": 653, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751886143616222, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751886143616275, "dur": 318, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751886143616674, "dur": 1626, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751886143618300, "dur": 156, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751886143618458, "dur": 290, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751886143618828, "dur": 716, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751886143619545, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751886143619695, "dur": 300, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751886143619996, "dur": 1337, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751886143621334, "dur": 2779648, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886143552864, "dur": 29669, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886143582555, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_2784FA9BFF134207.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751886143582928, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886143583204, "dur": 219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886143583424, "dur": 326, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_6A35B4E7D99F65C8.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751886143583752, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_FB81041CC44652D8.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751886143584644, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751886143584863, "dur": 144, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751886143585113, "dur": 441, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1751886143585659, "dur": 396, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886143586056, "dur": 378, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886143586435, "dur": 324, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886143586760, "dur": 973, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886143587734, "dur": 293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886143588028, "dur": 300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886143588329, "dur": 298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886143588627, "dur": 470, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886143589098, "dur": 365, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886143589464, "dur": 599, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886143590064, "dur": 392, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886143590457, "dur": 359, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886143590817, "dur": 319, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886143591137, "dur": 316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886143591453, "dur": 305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886143591758, "dur": 340, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886143592098, "dur": 330, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886143592428, "dur": 321, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886143592749, "dur": 558, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886143593308, "dur": 379, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886143593688, "dur": 319, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886143594007, "dur": 335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886143594343, "dur": 492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886143594836, "dur": 334, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886143595170, "dur": 323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886143595494, "dur": 359, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886143596090, "dur": 1112, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Pooling\\ManualPool.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751886143595854, "dur": 1425, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886143597280, "dur": 303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886143597584, "dur": 643, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886143598227, "dur": 301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886143598528, "dur": 293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886143599171, "dur": 526, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\Plugins\\HID\\HIDParser.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751886143598821, "dur": 896, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886143599717, "dur": 608, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886143600326, "dur": 295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886143600674, "dur": 2798, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\Controls\\DiscreteButtonControl.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751886143600621, "dur": 3392, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886143604014, "dur": 677, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886143604692, "dur": 1683, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886143606376, "dur": 2833, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751886143609210, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886143609330, "dur": 245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751886143609613, "dur": 973, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751886143610587, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886143610770, "dur": 1508, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751886143612347, "dur": 1426, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751886143613774, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886143613918, "dur": 1479, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886143615429, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751886143615537, "dur": 104, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886143615672, "dur": 887, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1751886143616562, "dur": 53, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886143616666, "dur": 1600, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886143618271, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886143618415, "dur": 1241, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886143619656, "dur": 348, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886143620004, "dur": 1335, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886143621340, "dur": 2779681, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886143552930, "dur": 29629, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886143582568, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_7359E91D15751A95.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751886143582929, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_0197621F1ECC6D18.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751886143583412, "dur": 246, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_69C31F9D75BAAB88.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751886143583695, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886143584462, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751886143584723, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886143584983, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886143585124, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751886143585376, "dur": 9244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751886143594691, "dur": 3013, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886143597736, "dur": 365, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886143598102, "dur": 304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886143598407, "dur": 296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886143598704, "dur": 370, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886143599075, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886143599343, "dur": 378, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886143599721, "dur": 318, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886143600039, "dur": 442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886143600482, "dur": 484, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886143600966, "dur": 310, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886143601276, "dur": 309, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886143601585, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886143601874, "dur": 290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886143602164, "dur": 339, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886143602503, "dur": 308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886143602846, "dur": 317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886143603164, "dur": 333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886143603514, "dur": 421, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886143603935, "dur": 764, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886143604699, "dur": 1392, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886143606094, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751886143606354, "dur": 297, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751886143606656, "dur": 757, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751886143607414, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886143607524, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751886143607794, "dur": 2171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751886143609966, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886143610065, "dur": 1332, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751886143611398, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886143611506, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886143611612, "dur": 1530, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751886143613144, "dur": 518, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886143613730, "dur": 1805, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886143615535, "dur": 124, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886143615688, "dur": 889, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751886143616578, "dur": 103, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886143616681, "dur": 1640, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886143618321, "dur": 1328, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886143619649, "dur": 373, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886143620022, "dur": 1342, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886143621365, "dur": 2779646, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886143553601, "dur": 29059, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886143582668, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_2C2D2F6DBE48DE8F.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751886143582907, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_676A4B919D1115D9.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751886143583380, "dur": 162, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_3BACF753C5B41AC5.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751886143583692, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_5BB3CA76865503EA.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751886143584400, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751886143584578, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751886143584861, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751886143584997, "dur": 992, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886143585996, "dur": 547, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886143586561, "dur": 657, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886143587219, "dur": 10841, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\MenuPriority.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751886143587219, "dur": 11134, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886143598353, "dur": 321, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886143598942, "dur": 1754, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\Plugins\\XR\\Devices\\Oculus.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751886143598675, "dur": 2070, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886143600746, "dur": 1482, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\Actions\\InputBindingComposite.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751886143600745, "dur": 1782, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886143602528, "dur": 276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886143602843, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886143603108, "dur": 320, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886143603909, "dur": 854, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886143604764, "dur": 1888, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886143606654, "dur": 309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751886143607005, "dur": 942, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751886143607998, "dur": 123, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751886143608169, "dur": 323, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751886143608493, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886143608574, "dur": 910, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751886143609485, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886143609561, "dur": 978, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751886143610540, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886143610609, "dur": 973, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751886143611583, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886143611745, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886143611892, "dur": 1294, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886143613247, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886143613355, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886143613716, "dur": 151, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886143613872, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886143613956, "dur": 1802, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886143615758, "dur": 868, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886143616627, "dur": 1683, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886143618310, "dur": 1389, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886143619699, "dur": 245, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886143619989, "dur": 1283, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886143621273, "dur": 98828, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886143720102, "dur": 2681067, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886143553720, "dur": 28951, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886143582672, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_BD8ABEB80D593DCC.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751886143582913, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886143583400, "dur": 161, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_681D101710621A59.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751886143583706, "dur": 1059, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_40A52831373DC9B0.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751886143584842, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751886143585542, "dur": 1675, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886143587225, "dur": 363, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886143587692, "dur": 1342, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\Actions\\TrackAction.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751886143587588, "dur": 1655, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886143589244, "dur": 527, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886143589772, "dur": 327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886143590100, "dur": 331, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886143590432, "dur": 279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886143590711, "dur": 305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886143591017, "dur": 292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886143591309, "dur": 327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886143591636, "dur": 289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886143591925, "dur": 329, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886143592254, "dur": 296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886143592550, "dur": 320, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886143592870, "dur": 832, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886143593703, "dur": 297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886143594000, "dur": 410, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886143594411, "dur": 309, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886143594771, "dur": 328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886143595099, "dur": 314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886143595413, "dur": 330, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886143596073, "dur": 774, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Reflection\\Operators\\SubtractionHandler.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751886143595744, "dur": 1265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886143597010, "dur": 298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886143597309, "dur": 306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886143597616, "dur": 306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886143597922, "dur": 300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886143598222, "dur": 310, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886143598533, "dur": 286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886143598943, "dur": 3286, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\Plugins\\Steam\\SteamSupport.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751886143598819, "dur": 3560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886143602380, "dur": 279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886143602659, "dur": 282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886143602941, "dur": 337, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886143603472, "dur": 505, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework\\UnityEditor.TestRunner\\TestLaunchers\\PlaymodeLauncher.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751886143603278, "dur": 705, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886143603983, "dur": 773, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886143604757, "dur": 3252, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886143608011, "dur": 292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751886143608344, "dur": 1371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751886143609753, "dur": 836, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751886143610590, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886143610689, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886143610799, "dur": 376, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751886143611227, "dur": 365, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751886143611621, "dur": 876, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751886143612498, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886143612569, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886143612744, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751886143612864, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886143612994, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751886143613110, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886143613233, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886143613470, "dur": 257, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886143613728, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751886143613820, "dur": 1945, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886143615765, "dur": 855, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886143616620, "dur": 1717, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886143618337, "dur": 1298, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886143619687, "dur": 326, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886143620013, "dur": 1240, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886143621258, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886143621321, "dur": 2779664, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886143553009, "dur": 29567, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886143582581, "dur": 654, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_0E44AF698B21F99B.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751886143583405, "dur": 167, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_D397B83A68A481D1.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751886143583718, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_B9448EEF9FF8FB2A.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751886143584213, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751886143584500, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751886143584750, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886143584938, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751886143584996, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886143585336, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751886143585535, "dur": 1690, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886143587233, "dur": 450, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886143587684, "dur": 506, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886143588190, "dur": 317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886143588507, "dur": 341, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886143588849, "dur": 313, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886143589163, "dur": 309, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886143589473, "dur": 325, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886143589798, "dur": 318, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886143590117, "dur": 330, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886143590448, "dur": 315, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886143590763, "dur": 316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886143591079, "dur": 306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886143591386, "dur": 314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886143591701, "dur": 352, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886143592053, "dur": 326, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886143592379, "dur": 336, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886143592716, "dur": 326, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886143593139, "dur": 495, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886143593635, "dur": 324, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886143593959, "dur": 336, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886143594295, "dur": 917, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886143595213, "dur": 324, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886143595537, "dur": 340, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886143596086, "dur": 785, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Listeners\\UIInterfaces\\UnityOnPointerUpMessageListener.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751886143595877, "dur": 1107, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886143596985, "dur": 322, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886143597308, "dur": 317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886143597681, "dur": 3094, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Collections\\NonNullableCollection.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751886143597626, "dur": 3435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886143601062, "dur": 351, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886143601413, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886143601697, "dur": 293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886143602194, "dur": 1386, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy\\Editor\\Tool\\LaunchTool.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751886143601990, "dur": 1939, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886143603930, "dur": 774, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886143604720, "dur": 1362, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886143606084, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751886143606374, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751886143606606, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886143606731, "dur": 1050, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751886143607783, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886143607982, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751886143608083, "dur": 472, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886143608565, "dur": 265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751886143608868, "dur": 4176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751886143613046, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886143613148, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886143613404, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886143613663, "dur": 180, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.UI.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1751886143613845, "dur": 1677, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886143615522, "dur": 176, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886143615699, "dur": 918, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886143616622, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886143616706, "dur": 1582, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886143618300, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886143618403, "dur": 1235, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886143619681, "dur": 297, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886143619979, "dur": 1270, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886143621293, "dur": 2739429, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886146360766, "dur": 659, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1751886146360725, "dur": 703, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1751886146361486, "dur": 4805, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1751886146366297, "dur": 34730, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751886146410707, "dur": 2858, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 20696, "tid": 172, "ts": 1751886146446401, "dur": 4975, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 20696, "tid": 172, "ts": 1751886146451446, "dur": 4357, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 20696, "tid": 172, "ts": 1751886146438228, "dur": 20085, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}