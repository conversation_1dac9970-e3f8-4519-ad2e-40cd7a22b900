{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 20696, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 20696, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 20696, "tid": 230, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 20696, "tid": 230, "ts": 1751886648586909, "dur": 1994, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 20696, "tid": 230, "ts": 1751886648595017, "dur": 1788, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 20696, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 20696, "tid": 1, "ts": 1751886648264609, "dur": 9476, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 20696, "tid": 1, "ts": 1751886648274091, "dur": 65022, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 20696, "tid": 1, "ts": 1751886648339123, "dur": 68517, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 20696, "tid": 230, "ts": 1751886648596812, "dur": 19, "ph": "X", "name": "", "args": {}}, {"pid": 20696, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648261453, "dur": 13625, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648275083, "dur": 295273, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648277427, "dur": 4212, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648281648, "dur": 4772, "ph": "X", "name": "ProcessMessages 20496", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648286427, "dur": 477, "ph": "X", "name": "ReadAsync 20496", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648286908, "dur": 21, "ph": "X", "name": "ProcessMessages 20525", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648286931, "dur": 206, "ph": "X", "name": "ReadAsync 20525", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648287141, "dur": 2, "ph": "X", "name": "ProcessMessages 1015", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648287145, "dur": 112, "ph": "X", "name": "ReadAsync 1015", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648287262, "dur": 2, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648287267, "dur": 141, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648287412, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648287414, "dur": 122, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648287539, "dur": 1, "ph": "X", "name": "ProcessMessages 957", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648287543, "dur": 153, "ph": "X", "name": "ReadAsync 957", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648287700, "dur": 1, "ph": "X", "name": "ProcessMessages 129", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648287702, "dur": 183, "ph": "X", "name": "ReadAsync 129", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648287889, "dur": 2, "ph": "X", "name": "ProcessMessages 755", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648287892, "dur": 113, "ph": "X", "name": "ReadAsync 755", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648288009, "dur": 2, "ph": "X", "name": "ProcessMessages 1285", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648288012, "dur": 61, "ph": "X", "name": "ReadAsync 1285", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648288076, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648288078, "dur": 101, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648288182, "dur": 2, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648288186, "dur": 109, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648288298, "dur": 1, "ph": "X", "name": "ProcessMessages 628", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648288300, "dur": 98, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648288401, "dur": 1, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648288403, "dur": 146, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648288575, "dur": 1, "ph": "X", "name": "ProcessMessages 262", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648288578, "dur": 146, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648288750, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648288753, "dur": 145, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648288901, "dur": 2, "ph": "X", "name": "ProcessMessages 1178", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648288904, "dur": 484, "ph": "X", "name": "ReadAsync 1178", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648289392, "dur": 1, "ph": "X", "name": "ProcessMessages 735", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648289394, "dur": 192, "ph": "X", "name": "ReadAsync 735", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648289590, "dur": 26, "ph": "X", "name": "ProcessMessages 4341", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648289618, "dur": 113, "ph": "X", "name": "ReadAsync 4341", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648289733, "dur": 1, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648289737, "dur": 142, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648289883, "dur": 1, "ph": "X", "name": "ProcessMessages 1036", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648289885, "dur": 97, "ph": "X", "name": "ReadAsync 1036", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648289985, "dur": 1, "ph": "X", "name": "ProcessMessages 333", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648289987, "dur": 83, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648290074, "dur": 1, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648290076, "dur": 53, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648290132, "dur": 1, "ph": "X", "name": "ProcessMessages 373", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648290134, "dur": 94, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648290230, "dur": 1, "ph": "X", "name": "ProcessMessages 848", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648290232, "dur": 52, "ph": "X", "name": "ReadAsync 848", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648290289, "dur": 90, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648290384, "dur": 2, "ph": "X", "name": "ProcessMessages 951", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648290387, "dur": 55, "ph": "X", "name": "ReadAsync 951", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648290445, "dur": 1, "ph": "X", "name": "ProcessMessages 595", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648290447, "dur": 56, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648290506, "dur": 1, "ph": "X", "name": "ProcessMessages 391", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648290508, "dur": 60, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648290571, "dur": 1, "ph": "X", "name": "ProcessMessages 387", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648290573, "dur": 135, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648290712, "dur": 1, "ph": "X", "name": "ProcessMessages 498", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648290715, "dur": 150, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648290869, "dur": 2, "ph": "X", "name": "ProcessMessages 757", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648290872, "dur": 53, "ph": "X", "name": "ReadAsync 757", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648290929, "dur": 55, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648290987, "dur": 1, "ph": "X", "name": "ProcessMessages 519", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648290989, "dur": 54, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648291045, "dur": 1, "ph": "X", "name": "ProcessMessages 387", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648291047, "dur": 94, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648291143, "dur": 1, "ph": "X", "name": "ProcessMessages 406", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648291145, "dur": 54, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648291201, "dur": 1, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648291203, "dur": 55, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648291261, "dur": 1, "ph": "X", "name": "ProcessMessages 528", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648291263, "dur": 52, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648291318, "dur": 1, "ph": "X", "name": "ProcessMessages 414", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648291320, "dur": 90, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648291412, "dur": 1, "ph": "X", "name": "ProcessMessages 552", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648291415, "dur": 55, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648291473, "dur": 54, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648291530, "dur": 1, "ph": "X", "name": "ProcessMessages 474", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648291532, "dur": 151, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648291688, "dur": 2, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648291691, "dur": 49, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648291745, "dur": 1, "ph": "X", "name": "ProcessMessages 173", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648291747, "dur": 66, "ph": "X", "name": "ReadAsync 173", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648291818, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648291820, "dur": 72, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648291896, "dur": 2, "ph": "X", "name": "ProcessMessages 676", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648291899, "dur": 61, "ph": "X", "name": "ReadAsync 676", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648291964, "dur": 2, "ph": "X", "name": "ProcessMessages 618", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648291968, "dur": 59, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648292031, "dur": 1, "ph": "X", "name": "ProcessMessages 230", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648292034, "dur": 64, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648292102, "dur": 1, "ph": "X", "name": "ProcessMessages 122", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648292105, "dur": 63, "ph": "X", "name": "ReadAsync 122", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648292170, "dur": 1, "ph": "X", "name": "ProcessMessages 575", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648292173, "dur": 468, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648292647, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648292651, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648292715, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648292718, "dur": 110, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648292832, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648292835, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648292906, "dur": 2, "ph": "X", "name": "ProcessMessages 658", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648292909, "dur": 60, "ph": "X", "name": "ReadAsync 658", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648292973, "dur": 1, "ph": "X", "name": "ProcessMessages 308", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648292977, "dur": 83, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648293065, "dur": 1, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648293069, "dur": 65, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648293137, "dur": 1, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648293140, "dur": 45, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648293189, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648293192, "dur": 57, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648293253, "dur": 1, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648293256, "dur": 61, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648293321, "dur": 2, "ph": "X", "name": "ProcessMessages 719", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648293324, "dur": 58, "ph": "X", "name": "ReadAsync 719", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648293385, "dur": 1, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648293389, "dur": 54, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648293446, "dur": 2, "ph": "X", "name": "ProcessMessages 546", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648293449, "dur": 45, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648293498, "dur": 1, "ph": "X", "name": "ProcessMessages 90", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648293501, "dur": 55, "ph": "X", "name": "ReadAsync 90", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648293559, "dur": 1, "ph": "X", "name": "ProcessMessages 417", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648293562, "dur": 45, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648293610, "dur": 1, "ph": "X", "name": "ProcessMessages 235", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648293613, "dur": 42, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648293658, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648293660, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648293721, "dur": 1, "ph": "X", "name": "ProcessMessages 702", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648293724, "dur": 54, "ph": "X", "name": "ReadAsync 702", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648293781, "dur": 2, "ph": "X", "name": "ProcessMessages 446", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648293785, "dur": 52, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648293840, "dur": 5, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648293846, "dur": 57, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648293906, "dur": 1, "ph": "X", "name": "ProcessMessages 706", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648293909, "dur": 58, "ph": "X", "name": "ReadAsync 706", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648293972, "dur": 1, "ph": "X", "name": "ProcessMessages 632", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648293975, "dur": 60, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648294038, "dur": 1, "ph": "X", "name": "ProcessMessages 593", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648294041, "dur": 50, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648294095, "dur": 1, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648294098, "dur": 90, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648294192, "dur": 2, "ph": "X", "name": "ProcessMessages 664", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648294195, "dur": 67, "ph": "X", "name": "ReadAsync 664", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648294267, "dur": 1, "ph": "X", "name": "ProcessMessages 594", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648294270, "dur": 55, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648294328, "dur": 1, "ph": "X", "name": "ProcessMessages 423", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648294331, "dur": 56, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648294390, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648294393, "dur": 57, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648294453, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648294455, "dur": 64, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648294523, "dur": 1, "ph": "X", "name": "ProcessMessages 576", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648294526, "dur": 57, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648294587, "dur": 1, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648294589, "dur": 54, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648294647, "dur": 1, "ph": "X", "name": "ProcessMessages 447", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648294649, "dur": 59, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648294712, "dur": 1, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648294714, "dur": 55, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648294772, "dur": 2, "ph": "X", "name": "ProcessMessages 393", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648294776, "dur": 57, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648294836, "dur": 1, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648294840, "dur": 57, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648294900, "dur": 1, "ph": "X", "name": "ProcessMessages 591", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648294903, "dur": 51, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648294960, "dur": 1, "ph": "X", "name": "ProcessMessages 91", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648294964, "dur": 60, "ph": "X", "name": "ReadAsync 91", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648295027, "dur": 2, "ph": "X", "name": "ProcessMessages 724", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648295030, "dur": 61, "ph": "X", "name": "ReadAsync 724", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648295094, "dur": 1, "ph": "X", "name": "ProcessMessages 665", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648295097, "dur": 58, "ph": "X", "name": "ReadAsync 665", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648295158, "dur": 2, "ph": "X", "name": "ProcessMessages 628", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648295163, "dur": 50, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648295216, "dur": 1, "ph": "X", "name": "ProcessMessages 364", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648295219, "dur": 52, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648295277, "dur": 49, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648295330, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648295332, "dur": 143, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648295480, "dur": 1, "ph": "X", "name": "ProcessMessages 543", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648295483, "dur": 75, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648295562, "dur": 3, "ph": "X", "name": "ProcessMessages 1082", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648295566, "dur": 54, "ph": "X", "name": "ReadAsync 1082", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648295623, "dur": 1, "ph": "X", "name": "ProcessMessages 283", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648295626, "dur": 58, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648295687, "dur": 1, "ph": "X", "name": "ProcessMessages 416", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648295690, "dur": 58, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648295751, "dur": 1, "ph": "X", "name": "ProcessMessages 603", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648295754, "dur": 58, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648295815, "dur": 1, "ph": "X", "name": "ProcessMessages 622", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648295818, "dur": 50, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648295871, "dur": 1, "ph": "X", "name": "ProcessMessages 369", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648295874, "dur": 54, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648295931, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648295933, "dur": 61, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648295997, "dur": 2, "ph": "X", "name": "ProcessMessages 644", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648296000, "dur": 56, "ph": "X", "name": "ReadAsync 644", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648296059, "dur": 1, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648296062, "dur": 58, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648296124, "dur": 2, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648296127, "dur": 66, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648296196, "dur": 1, "ph": "X", "name": "ProcessMessages 177", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648296199, "dur": 59, "ph": "X", "name": "ReadAsync 177", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648296261, "dur": 1, "ph": "X", "name": "ProcessMessages 733", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648296264, "dur": 57, "ph": "X", "name": "ReadAsync 733", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648296324, "dur": 1, "ph": "X", "name": "ProcessMessages 547", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648296327, "dur": 58, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648296388, "dur": 2, "ph": "X", "name": "ProcessMessages 462", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648296391, "dur": 45, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648296440, "dur": 1, "ph": "X", "name": "ProcessMessages 239", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648296444, "dur": 50, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648296498, "dur": 1, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648296501, "dur": 58, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648296562, "dur": 1, "ph": "X", "name": "ProcessMessages 782", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648296565, "dur": 55, "ph": "X", "name": "ReadAsync 782", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648296623, "dur": 2, "ph": "X", "name": "ProcessMessages 586", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648296626, "dur": 57, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648296686, "dur": 2, "ph": "X", "name": "ProcessMessages 661", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648296690, "dur": 72, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648296766, "dur": 1, "ph": "X", "name": "ProcessMessages 102", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648296769, "dur": 104, "ph": "X", "name": "ReadAsync 102", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648296877, "dur": 2, "ph": "X", "name": "ProcessMessages 962", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648296881, "dur": 57, "ph": "X", "name": "ReadAsync 962", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648296943, "dur": 2, "ph": "X", "name": "ProcessMessages 133", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648296946, "dur": 60, "ph": "X", "name": "ReadAsync 133", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648297010, "dur": 1, "ph": "X", "name": "ProcessMessages 553", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648297013, "dur": 55, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648297071, "dur": 1, "ph": "X", "name": "ProcessMessages 433", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648297074, "dur": 49, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648297126, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648297128, "dur": 61, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648297193, "dur": 2, "ph": "X", "name": "ProcessMessages 640", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648297197, "dur": 69, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648297269, "dur": 1, "ph": "X", "name": "ProcessMessages 528", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648297271, "dur": 57, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648297332, "dur": 2, "ph": "X", "name": "ProcessMessages 450", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648297335, "dur": 53, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648297391, "dur": 1, "ph": "X", "name": "ProcessMessages 455", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648297394, "dur": 56, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648297454, "dur": 1, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648297456, "dur": 58, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648297517, "dur": 2, "ph": "X", "name": "ProcessMessages 473", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648297521, "dur": 54, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648297579, "dur": 1, "ph": "X", "name": "ProcessMessages 403", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648297583, "dur": 59, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648297645, "dur": 1, "ph": "X", "name": "ProcessMessages 492", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648297648, "dur": 53, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648297704, "dur": 1, "ph": "X", "name": "ProcessMessages 275", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648297706, "dur": 56, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648297765, "dur": 1, "ph": "X", "name": "ProcessMessages 424", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648297768, "dur": 63, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648297834, "dur": 2, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648297837, "dur": 55, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648297896, "dur": 2, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648297899, "dur": 55, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648297958, "dur": 2, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648297961, "dur": 62, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648298027, "dur": 1, "ph": "X", "name": "ProcessMessages 663", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648298030, "dur": 55, "ph": "X", "name": "ReadAsync 663", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648298088, "dur": 1, "ph": "X", "name": "ProcessMessages 515", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648298091, "dur": 56, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648298151, "dur": 2, "ph": "X", "name": "ProcessMessages 474", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648298154, "dur": 60, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648298219, "dur": 2, "ph": "X", "name": "ProcessMessages 677", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648298222, "dur": 95, "ph": "X", "name": "ReadAsync 677", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648298322, "dur": 2, "ph": "X", "name": "ProcessMessages 696", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648298326, "dur": 49, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648298380, "dur": 1, "ph": "X", "name": "ProcessMessages 14", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648298382, "dur": 53, "ph": "X", "name": "ReadAsync 14", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648298438, "dur": 2, "ph": "X", "name": "ProcessMessages 327", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648298441, "dur": 93, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648298538, "dur": 2, "ph": "X", "name": "ProcessMessages 461", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648298541, "dur": 44, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648298588, "dur": 2, "ph": "X", "name": "ProcessMessages 47", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648298591, "dur": 57, "ph": "X", "name": "ReadAsync 47", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648298652, "dur": 2, "ph": "X", "name": "ProcessMessages 480", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648298656, "dur": 55, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648298714, "dur": 2, "ph": "X", "name": "ProcessMessages 651", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648298717, "dur": 54, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648298775, "dur": 1, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648298777, "dur": 54, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648298834, "dur": 1, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648298837, "dur": 48, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648298888, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648298890, "dur": 57, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648298951, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648298954, "dur": 95, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648299051, "dur": 2, "ph": "X", "name": "ProcessMessages 704", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648299055, "dur": 42, "ph": "X", "name": "ReadAsync 704", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648299100, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648299102, "dur": 56, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648299161, "dur": 1, "ph": "X", "name": "ProcessMessages 402", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648299165, "dur": 91, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648299259, "dur": 2, "ph": "X", "name": "ProcessMessages 467", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648299262, "dur": 47, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648299312, "dur": 1, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648299314, "dur": 49, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648299366, "dur": 1, "ph": "X", "name": "ProcessMessages 276", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648299370, "dur": 94, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648299467, "dur": 2, "ph": "X", "name": "ProcessMessages 673", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648299471, "dur": 43, "ph": "X", "name": "ReadAsync 673", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648299517, "dur": 1, "ph": "X", "name": "ProcessMessages 1", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648299519, "dur": 94, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648299617, "dur": 2, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648299620, "dur": 44, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648299668, "dur": 68, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648299741, "dur": 2, "ph": "X", "name": "ProcessMessages 695", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648299745, "dur": 62, "ph": "X", "name": "ReadAsync 695", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648299810, "dur": 1, "ph": "X", "name": "ProcessMessages 612", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648299814, "dur": 51, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648299868, "dur": 2, "ph": "X", "name": "ProcessMessages 357", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648299871, "dur": 55, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648299929, "dur": 1, "ph": "X", "name": "ProcessMessages 462", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648299931, "dur": 52, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648299987, "dur": 3, "ph": "X", "name": "ProcessMessages 327", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648299991, "dur": 55, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648300049, "dur": 2, "ph": "X", "name": "ProcessMessages 562", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648300053, "dur": 56, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648300112, "dur": 2, "ph": "X", "name": "ProcessMessages 566", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648300115, "dur": 58, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648300176, "dur": 2, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648300179, "dur": 56, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648300238, "dur": 2, "ph": "X", "name": "ProcessMessages 575", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648300241, "dur": 58, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648300302, "dur": 2, "ph": "X", "name": "ProcessMessages 530", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648300306, "dur": 57, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648300365, "dur": 2, "ph": "X", "name": "ProcessMessages 519", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648300368, "dur": 56, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648300428, "dur": 2, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648300431, "dur": 57, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648300491, "dur": 2, "ph": "X", "name": "ProcessMessages 557", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648300494, "dur": 53, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648300551, "dur": 2, "ph": "X", "name": "ProcessMessages 597", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648300554, "dur": 58, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648300616, "dur": 2, "ph": "X", "name": "ProcessMessages 656", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648300619, "dur": 57, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648300679, "dur": 1, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648300682, "dur": 66, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648300752, "dur": 2, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648300755, "dur": 66, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648300826, "dur": 2, "ph": "X", "name": "ProcessMessages 582", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648300829, "dur": 102, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648300935, "dur": 1, "ph": "X", "name": "ProcessMessages 264", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648300939, "dur": 49, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648300992, "dur": 2, "ph": "X", "name": "ProcessMessages 152", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648300996, "dur": 60, "ph": "X", "name": "ReadAsync 152", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648301060, "dur": 2, "ph": "X", "name": "ProcessMessages 215", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648301064, "dur": 222, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648301291, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648301293, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648301348, "dur": 524, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648301877, "dur": 90, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648301971, "dur": 8, "ph": "X", "name": "ProcessMessages 756", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648301981, "dur": 53, "ph": "X", "name": "ReadAsync 756", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648302038, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648302041, "dur": 47, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648302091, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648302094, "dur": 67, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648302165, "dur": 2, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648302169, "dur": 55, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648302228, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648302231, "dur": 50, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648302285, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648302287, "dur": 45, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648302338, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648302341, "dur": 42, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648302388, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648302392, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648302425, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648302428, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648302473, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648302475, "dur": 39, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648302517, "dur": 2, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648302520, "dur": 37, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648302560, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648302563, "dur": 45, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648302612, "dur": 2, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648302617, "dur": 47, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648302669, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648302672, "dur": 45, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648302720, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648302724, "dur": 43, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648302770, "dur": 2, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648302773, "dur": 39, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648302817, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648302820, "dur": 40, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648302864, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648302867, "dur": 42, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648302912, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648302914, "dur": 37, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648302955, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648302958, "dur": 35, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648302996, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648303000, "dur": 42, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648303045, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648303048, "dur": 36, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648303087, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648303089, "dur": 41, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648303133, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648303135, "dur": 39, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648303178, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648303181, "dur": 45, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648303229, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648303231, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648303273, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648303276, "dur": 39, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648303317, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648303320, "dur": 37, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648303360, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648303362, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648303401, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648303405, "dur": 54, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648303463, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648303465, "dur": 37, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648303506, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648303509, "dur": 56, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648303569, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648303572, "dur": 38, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648303613, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648303615, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648303655, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648303657, "dur": 34, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648303694, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648303697, "dur": 37, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648303737, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648303740, "dur": 37, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648303780, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648303782, "dur": 37, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648303824, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648303826, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648303870, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648303873, "dur": 39, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648303916, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648303918, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648303961, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648303965, "dur": 41, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648304010, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648304013, "dur": 38, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648304055, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648304057, "dur": 47, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648304107, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648304110, "dur": 46, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648304159, "dur": 2, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648304162, "dur": 39, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648304204, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648304207, "dur": 45, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648304255, "dur": 2, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648304258, "dur": 38, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648304299, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648304302, "dur": 42, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648304347, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648304352, "dur": 36, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648304394, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648304396, "dur": 37, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648304437, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648304440, "dur": 42, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648304487, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648304489, "dur": 37, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648304530, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648304532, "dur": 37, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648304572, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648304574, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648304615, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648304618, "dur": 50, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648304672, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648304675, "dur": 48, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648304729, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648304733, "dur": 47, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648304785, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648304789, "dur": 39, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648304831, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648304834, "dur": 666, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648305505, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648305507, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648305560, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648305563, "dur": 2106, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648307674, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648307677, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648307722, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648307725, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648307763, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648307766, "dur": 710, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648308480, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648308482, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648308522, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648308524, "dur": 83, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648308609, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648308611, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648308648, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648308652, "dur": 210, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648308865, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648308867, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648308906, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648308910, "dur": 6376, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648315294, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648315298, "dur": 113, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648315416, "dur": 3, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648315421, "dur": 225, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648315650, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648315653, "dur": 86, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648315743, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648315747, "dur": 720, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648316470, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648316473, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648316526, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648316529, "dur": 164, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648316698, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648316700, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648316744, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648316747, "dur": 44, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648316794, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648316796, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648316830, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648316832, "dur": 131, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648316966, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648316969, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648317012, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648317015, "dur": 379, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648317398, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648317400, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648317445, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648317447, "dur": 36, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648317486, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648317489, "dur": 54, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648317563, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648317566, "dur": 69, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648317639, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648317642, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648317703, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648317706, "dur": 150, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648317860, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648317862, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648317910, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648317913, "dur": 38, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648317953, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648317956, "dur": 47, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648318006, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648318009, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648318060, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648318063, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648318101, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648318105, "dur": 197, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648318306, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648318308, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648318350, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648318353, "dur": 43, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648318401, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648318435, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648318437, "dur": 392, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648318833, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648318835, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648318882, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648318885, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648318938, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648318940, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648318979, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648318981, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648319018, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648319054, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648319098, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648319135, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648319138, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648319176, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648319178, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648319218, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648319252, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648319255, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648319289, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648319292, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648319327, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648319329, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648319370, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648319372, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648319410, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648319413, "dur": 109, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648319527, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648319562, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648319564, "dur": 164, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648319732, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648319736, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648319780, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648319783, "dur": 69, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648319856, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648319894, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648319896, "dur": 54, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648319953, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648319955, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648319989, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648319991, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648320025, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648320028, "dur": 64, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648320096, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648320131, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648320135, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648320170, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648320173, "dur": 163, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648320339, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648320341, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648320374, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648320376, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648320415, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648320418, "dur": 49, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648320470, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648320472, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648320511, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648320514, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648320548, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648320551, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648320587, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648320590, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648320629, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648320632, "dur": 60, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648320697, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648320731, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648320733, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648320767, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648320803, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648320806, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648320839, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648320842, "dur": 36, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648320881, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648320883, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648320918, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648320920, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648320962, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648320965, "dur": 35, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648321003, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648321007, "dur": 35, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648321045, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648321047, "dur": 40, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648321090, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648321092, "dur": 53, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648321149, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648321152, "dur": 49, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648321205, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648321207, "dur": 184, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648321394, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648321396, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648321453, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648321456, "dur": 54, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648321514, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648321517, "dur": 45, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648321566, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648321570, "dur": 49, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648321623, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648321625, "dur": 204, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648321834, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648321838, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648321892, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648321895, "dur": 1714, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648323615, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648323619, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648323673, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648323676, "dur": 58, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648323739, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648323743, "dur": 176, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648323923, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648323926, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648323979, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648323982, "dur": 567, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648324553, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648324555, "dur": 134, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648324695, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648324699, "dur": 186, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648324890, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648324892, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648324950, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648324953, "dur": 217, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648325174, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648325177, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648325233, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648325235, "dur": 545, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648325785, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648325787, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648325830, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648325832, "dur": 79, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648325915, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648325917, "dur": 71, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648325994, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648325998, "dur": 46, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648326050, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648326053, "dur": 169, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648326227, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648326231, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648326298, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648326302, "dur": 438, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648326744, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648326747, "dur": 127, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648326879, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648326883, "dur": 51, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648326939, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648326943, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648326991, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648326994, "dur": 1266, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648328266, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648328271, "dur": 71, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648328347, "dur": 1035, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648329391, "dur": 198215, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648527618, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648527623, "dur": 69, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648527698, "dur": 1, "ph": "X", "name": "ProcessMessages 212", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648527712, "dur": 50, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648527765, "dur": 1719, "ph": "X", "name": "ProcessMessages 107", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648529490, "dur": 1420, "ph": "X", "name": "ReadAsync 107", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648530935, "dur": 9, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648530957, "dur": 149, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648531115, "dur": 11, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648531130, "dur": 2779, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648533917, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648533921, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648533982, "dur": 46, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648534031, "dur": 24368, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648558409, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648558415, "dur": 113, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648558533, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648558539, "dur": 1105, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648559656, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648559660, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648559732, "dur": 30, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648559764, "dur": 519, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648560289, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648560292, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648560346, "dur": 549, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 20696, "tid": 12884901888, "ts": 1751886648560900, "dur": 9199, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 20696, "tid": 230, "ts": 1751886648596834, "dur": 1547, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 20696, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 20696, "tid": 8589934592, "ts": 1751886648255753, "dur": 152042, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 20696, "tid": 8589934592, "ts": 1751886648407798, "dur": 7, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 20696, "tid": 8589934592, "ts": 1751886648407807, "dur": 2316, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 20696, "tid": 230, "ts": 1751886648598383, "dur": 6, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 20696, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 20696, "tid": 4294967296, "ts": 1751886648228060, "dur": 344672, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 20696, "tid": 4294967296, "ts": 1751886648233675, "dur": 10961, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 20696, "tid": 4294967296, "ts": 1751886648573121, "dur": 8921, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 20696, "tid": 4294967296, "ts": 1751886648578221, "dur": 170, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 20696, "tid": 4294967296, "ts": 1751886648582185, "dur": 21, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 20696, "tid": 230, "ts": 1751886648598391, "dur": 8, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1751886648270554, "dur": 118, "ph": "X", "name": "IPC_Client_InitializeAndConnectToParent", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751886648270733, "dur": 3755, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751886648274527, "dur": 710, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751886648275314, "dur": 67, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1751886648275382, "dur": 639, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751886648276311, "dur": 217, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_2784FA9BFF134207.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751886648276578, "dur": 160, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_7359E91D15751A95.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751886648276852, "dur": 100, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_31DB0EF841A9843D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751886648277042, "dur": 96, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_CDB992C8B5C82ED1.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751886648278395, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_42529D1BD0B2CBE0.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751886648278782, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_11834CDAF2387C83.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751886648279107, "dur": 1894, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_3BACF753C5B41AC5.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751886648282079, "dur": 76, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_FAA2D6271BC6A85C.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751886648282954, "dur": 4268, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751886648287296, "dur": 127, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751886648287693, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751886648287778, "dur": 126, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751886648288250, "dur": 132, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751886648288558, "dur": 90, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751886648288673, "dur": 82, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751886648289000, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751886648289123, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751886648289763, "dur": 192, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.ref.dll_D3B9364D6FB03093.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751886648289961, "dur": 102, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751886648290090, "dur": 143, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751886648290239, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751886648290357, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751886648290486, "dur": 118, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751886648290655, "dur": 78, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751886648291089, "dur": 140, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751886648291402, "dur": 117, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751886648291671, "dur": 114, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751886648291896, "dur": 157, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751886648292192, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751886648292251, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751886648292330, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751886648292990, "dur": 184, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751886648293279, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751886648293340, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751886648294484, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751886648295840, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751886648297130, "dur": 116, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751886648298586, "dur": 122, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751886648298812, "dur": 118, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751886648299023, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751886648299311, "dur": 114, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751886648299537, "dur": 118, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751886648299733, "dur": 120, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.ForUI.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751886648299881, "dur": 122, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751886648300096, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751886648301121, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751886648301185, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751886648276080, "dur": 25273, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751886648301371, "dur": 258873, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751886648560245, "dur": 98, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751886648560470, "dur": 107, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751886648560607, "dur": 1881, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1751886648276464, "dur": 24922, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751886648301404, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_2784FA9BFF134207.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751886648301672, "dur": 146, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_7D12CF9E98D9D784.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751886648302110, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_DB68F65C9F3573CD.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751886648302299, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_3BACF753C5B41AC5.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751886648302471, "dur": 151, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_5E3C6CF57067B635.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751886648302624, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_7882E8644625FF23.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751886648303041, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_F04876DA9D7398DA.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751886648303322, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751886648303410, "dur": 258, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751886648303695, "dur": 4249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751886648308063, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751886648308352, "dur": 280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751886648308632, "dur": 282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751886648308947, "dur": 381, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751886648309329, "dur": 424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751886648309754, "dur": 309, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751886648310063, "dur": 340, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751886648310547, "dur": 523, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Variables\\VariableKind.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751886648310404, "dur": 809, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751886648311213, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751886648311478, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751886648311745, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751886648312010, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751886648312277, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751886648312522, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751886648312766, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751886648313021, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751886648313645, "dur": 1558, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy\\Editor\\Views\\PendingChanges\\UnityPendingChangesTree.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751886648313278, "dur": 2052, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751886648315330, "dur": 272, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751886648315644, "dur": 1119, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751886648316764, "dur": 104, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751886648316870, "dur": 399, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751886648317308, "dur": 2707, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751886648320016, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751886648320099, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751886648320200, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751886648320450, "dur": 1236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751886648321768, "dur": 123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751886648321891, "dur": 2081, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751886648324016, "dur": 899, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751886648324915, "dur": 299, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751886648325215, "dur": 1101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751886648326317, "dur": 773, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751886648327129, "dur": 233051, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886648276551, "dur": 24857, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886648301415, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_7359E91D15751A95.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751886648302281, "dur": 256, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_0D58F8E6C2E92DF4.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751886648302564, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886648302740, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_7FF6DD7231CA2BE3.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751886648303098, "dur": 347, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1751886648303447, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751886648303539, "dur": 192, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751886648303773, "dur": 131, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1751886648303906, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751886648303971, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886648304047, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886648304139, "dur": 178, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1751886648304318, "dur": 289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751886648304614, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886648304678, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751886648304729, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751886648305053, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886648305118, "dur": 591, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886648305710, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886648305971, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886648306236, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886648306496, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886648306741, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886648306992, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886648307246, "dur": 299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886648307546, "dur": 549, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886648308095, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886648308356, "dur": 311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886648308667, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886648308963, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886648309219, "dur": 461, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886648309681, "dur": 282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886648310550, "dur": 520, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Core\\Inspection\\Primitives\\DiscreteNumberInspector.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751886648309964, "dur": 1107, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886648311071, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886648311343, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886648311605, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886648311877, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886648312136, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886648312447, "dur": 968, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\Plugins\\XInput\\XboxGamepadMacOS.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751886648312395, "dur": 1222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886648313618, "dur": 368, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886648313986, "dur": 282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886648314269, "dur": 291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886648314560, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886648314752, "dur": 487, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886648315240, "dur": 369, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886648315609, "dur": 1163, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886648316808, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751886648317037, "dur": 616, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751886648317654, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886648317755, "dur": 283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751886648318039, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886648318221, "dur": 1442, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751886648319740, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886648319880, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751886648320107, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751886648320345, "dur": 771, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751886648321194, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.CollabProxy.Editor.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1751886648321308, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886648321436, "dur": 315, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886648321751, "dur": 130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886648321914, "dur": 2007, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886648324040, "dur": 848, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886648324922, "dur": 288, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886648325242, "dur": 896, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886648326170, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886648326268, "dur": 833, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886648327101, "dur": 202982, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751886648530133, "dur": 904, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1751886648530086, "dur": 957, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1751886648531177, "dur": 3062, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1751886648534245, "dur": 25781, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751886648276829, "dur": 24596, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751886648301430, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_0E44AF698B21F99B.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751886648301583, "dur": 156, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_0E44AF698B21F99B.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751886648302275, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_782156EAC64F1FC2.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751886648302381, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_C1FAB9A8897A5958.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751886648302491, "dur": 140, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_C1FAB9A8897A5958.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751886648302633, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_FB81041CC44652D8.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751886648302947, "dur": 442, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_92A1593FB10DA16D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751886648303406, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751886648303649, "dur": 5112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751886648308845, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751886648309057, "dur": 6462, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751886648315634, "dur": 284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751886648315969, "dur": 687, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751886648316807, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751886648317102, "dur": 2369, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751886648319472, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751886648319633, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751886648319864, "dur": 194, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751886648320060, "dur": 1765, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751886648321907, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751886648322158, "dur": 1627, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751886648323786, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751886648323965, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751886648324212, "dur": 594, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751886648324909, "dur": 304, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751886648325247, "dur": 893, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751886648326279, "dur": 813, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751886648327121, "dur": 232950, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886648277223, "dur": 24215, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886648301442, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_AAC1DDBB583EF4F7.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751886648301601, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886648301686, "dur": 717, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_AAC1DDBB583EF4F7.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751886648302477, "dur": 127, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_805E31AC8FEB08D0.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751886648302606, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_1D503C7A8290C7BE.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751886648302940, "dur": 114, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_DC466A201EF621B1.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751886648303114, "dur": 101, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_E43C745D5056CF76.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751886648303345, "dur": 340, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751886648303687, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751886648303782, "dur": 133, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751886648303959, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886648304130, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886648304208, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886648304278, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751886648304383, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886648304482, "dur": 580, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886648305067, "dur": 410, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751886648305478, "dur": 340, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886648305831, "dur": 366, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886648306197, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886648306468, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886648306739, "dur": 521, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886648307261, "dur": 278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886648307539, "dur": 529, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886648308068, "dur": 369, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886648308437, "dur": 332, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886648308770, "dur": 278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886648309048, "dur": 293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886648309342, "dur": 947, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886648310289, "dur": 780, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886648311070, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886648311328, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886648311593, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886648311848, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886648312108, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886648312379, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886648312622, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886648312883, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886648313144, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886648313382, "dur": 713, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886648314096, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886648314359, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886648314667, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886648314933, "dur": 403, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886648315337, "dur": 279, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886648315616, "dur": 1149, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886648316809, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751886648317051, "dur": 983, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751886648318035, "dur": 1300, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886648319383, "dur": 320, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751886648319739, "dur": 925, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751886648320947, "dur": 228, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886648321203, "dur": 196, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.ForUI.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1751886648321400, "dur": 355, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886648321755, "dur": 142, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886648321897, "dur": 2059, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886648323956, "dur": 78, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886648324034, "dur": 867, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886648324901, "dur": 310, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886648325241, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751886648325463, "dur": 594, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751886648326128, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.SettingsProvider.Editor.ref.dll_A1A44354E0B583A8.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751886648326326, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886648326548, "dur": 549, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886648327097, "dur": 84764, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751886648411862, "dur": 148313, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886648277288, "dur": 24165, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886648301460, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_AD3F2B6EFF130063.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751886648301580, "dur": 255, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_AD3F2B6EFF130063.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751886648302284, "dur": 126, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_9923BABDF0F3077F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751886648302483, "dur": 129, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_7B05F0D16B4953B8.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751886648302615, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_B9448EEF9FF8FB2A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751886648302769, "dur": 223, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_B9448EEF9FF8FB2A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751886648303091, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_B1D6EB5640F05713.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751886648303171, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_9E61C56C19D47066.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751886648303241, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886648303313, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_9E61C56C19D47066.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751886648303504, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751886648303630, "dur": 805, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751886648304510, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886648304630, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751886648304830, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886648305100, "dur": 574, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886648305675, "dur": 748, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886648306424, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886648306696, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886648307005, "dur": 623, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.State\\Plugin\\Migrations\\Migration_1_6_to_1_7.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751886648306942, "dur": 891, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886648307833, "dur": 334, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886648308167, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886648308426, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886648308688, "dur": 646, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886648309334, "dur": 1012, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886648310552, "dur": 520, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Core\\Analytics\\Analytics.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751886648310346, "dur": 784, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886648311130, "dur": 300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886648311431, "dur": 310, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886648311742, "dur": 290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886648312033, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886648312292, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886648312566, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886648312810, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886648313064, "dur": 374, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886648313438, "dur": 280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886648313718, "dur": 433, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886648314152, "dur": 336, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886648314488, "dur": 287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886648314775, "dur": 461, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886648315236, "dur": 378, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886648315614, "dur": 1148, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886648318057, "dur": 557, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751886648318660, "dur": 546, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751886648319207, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886648319280, "dur": 1342, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751886648320686, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886648320745, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886648320942, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886648321040, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886648321471, "dur": 289, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886648321760, "dur": 132, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886648321893, "dur": 2073, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886648323966, "dur": 55, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886648324021, "dur": 889, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886648324910, "dur": 306, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886648325216, "dur": 1074, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886648326290, "dur": 818, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751886648327108, "dur": 233098, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886648277332, "dur": 24132, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886648301469, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_31DB0EF841A9843D.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751886648301686, "dur": 629, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_78F0F79A0FB195D1.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751886648302419, "dur": 136, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_EABB3BB0B4DAF932.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751886648302557, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_4E97288DD1B11317.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751886648302764, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_C7DF13E3C14DE501.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751886648302844, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_CCD605E26FC2B53B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751886648302943, "dur": 144, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_CCD605E26FC2B53B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751886648303348, "dur": 236, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751886648303605, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886648303677, "dur": 502, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1751886648305031, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886648305151, "dur": 291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886648305442, "dur": 521, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886648305964, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886648306234, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886648306490, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886648306761, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886648307025, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886648307276, "dur": 305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886648307581, "dur": 742, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886648308323, "dur": 291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886648308615, "dur": 473, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886648309088, "dur": 318, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886648309407, "dur": 310, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886648309718, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886648310209, "dur": 871, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Core\\Inspection\\InspectorUtility.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751886648309993, "dur": 1182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886648311175, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886648311444, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886648311703, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886648311959, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886648312217, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886648312470, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886648312711, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886648312971, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886648313222, "dur": 284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886648313506, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886648313771, "dur": 398, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886648314170, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886648314427, "dur": 279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886648314707, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886648315195, "dur": 409, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886648315640, "dur": 1120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886648316921, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751886648317152, "dur": 593, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751886648317745, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886648317996, "dur": 365, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751886648318397, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751886648318639, "dur": 627, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751886648319312, "dur": 829, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751886648320225, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751886648320435, "dur": 332, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751886648320769, "dur": 605, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751886648321448, "dur": 299, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886648321779, "dur": 101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886648321910, "dur": 2039, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886648323949, "dur": 63, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886648324012, "dur": 878, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886648324891, "dur": 328, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886648325219, "dur": 931, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886648326150, "dur": 119, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886648326269, "dur": 826, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886648327096, "dur": 83388, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886648411509, "dur": 328, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "E:/Unity/Editior/6000.0.34f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 6, "ts": 1751886648410485, "dur": 1363, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751886648411849, "dur": 148195, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886648277400, "dur": 24080, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886648301484, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_2C2D2F6DBE48DE8F.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751886648301539, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886648301645, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_6BC1B44B0B58CEF0.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751886648301785, "dur": 482, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_0D411EBC79DB1BBD.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751886648302474, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_92A5F53C232CE1D9.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751886648302568, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_5BB3CA76865503EA.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751886648302694, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886648302764, "dur": 377, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_5BB3CA76865503EA.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751886648303480, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751886648303598, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751886648303756, "dur": 174, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1751886648304144, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751886648304240, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886648304292, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751886648304868, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751886648305149, "dur": 584, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886648305733, "dur": 316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886648306050, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886648306313, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886648306570, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886648306846, "dur": 282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886648307129, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886648307385, "dur": 412, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886648307798, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886648308084, "dur": 289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886648308374, "dur": 305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886648308680, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886648308943, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886648309180, "dur": 419, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886648309599, "dur": 681, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886648310280, "dur": 278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886648310559, "dur": 2871, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Variables\\ApplicationVariables.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751886648313650, "dur": 1243, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Utilities\\EnumUtility.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751886648310559, "dur": 4382, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886648314941, "dur": 84, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886648315180, "dur": 426, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886648315607, "dur": 1167, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886648316812, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751886648317088, "dur": 587, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751886648317676, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886648317864, "dur": 276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751886648318189, "dur": 513, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751886648318741, "dur": 564, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751886648319306, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886648319575, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751886648319647, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886648319725, "dur": 454, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751886648320179, "dur": 311, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886648320496, "dur": 596, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751886648321205, "dur": 219, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.EditorCoroutines.Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751886648321425, "dur": 321, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886648321786, "dur": 92, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886648321909, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751886648322147, "dur": 1780, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751886648323928, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886648324033, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751886648324275, "dur": 843, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751886648325240, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751886648325466, "dur": 602, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751886648326074, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886648326243, "dur": 287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751886648326562, "dur": 432, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751886648326996, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886648327116, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751886648327301, "dur": 373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751886648328100, "dur": 59, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751886648329546, "dur": 198400, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751886648530091, "dur": 28523, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751886648530074, "dur": 28542, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751886648558658, "dur": 1312, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751886648277457, "dur": 24032, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886648301539, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886648301664, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_22D039E8A3335822.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751886648301787, "dur": 436, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886648302276, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_F0DA661B5DCD99FC.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751886648302449, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_D8B62AD188B30A07.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751886648302571, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_40A52831373DC9B0.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751886648302768, "dur": 213, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_A44562DC2280D3AD.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751886648303208, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886648303514, "dur": 129, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1751886648303670, "dur": 131, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751886648303899, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751886648304167, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886648304235, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751886648304356, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886648305035, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886648305107, "dur": 836, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886648305944, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886648306201, "dur": 578, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886648306780, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886648307049, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886648307299, "dur": 295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886648308079, "dur": 1167, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Flow\\INesterUnit.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751886648309246, "dur": 1028, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Flow\\IDefaultValue.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751886648307594, "dur": 2743, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886648310547, "dur": 510, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Core\\Canvases\\ICanvas.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751886648310337, "dur": 774, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886648311112, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886648311380, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886648311646, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886648311901, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886648312153, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886648312398, "dur": 285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886648312683, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886648312936, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886648313174, "dur": 636, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886648313810, "dur": 289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886648314100, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886648314358, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886648314658, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886648314870, "dur": 442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886648315312, "dur": 296, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886648315608, "dur": 1164, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886648316814, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751886648317044, "dur": 700, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751886648317744, "dur": 94, "ph": "X", "name": "CheckPristineOutputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751886648317838, "dur": 417, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886648318292, "dur": 585, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751886648318879, "dur": 278, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886648319170, "dur": 1061, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751886648320232, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886648320321, "dur": 912, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751886648321402, "dur": 347, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886648321750, "dur": 148, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886648321898, "dur": 2027, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886648323929, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886648324044, "dur": 842, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886648324930, "dur": 292, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886648325222, "dur": 979, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886648326223, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886648326315, "dur": 788, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751886648327103, "dur": 233126, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751886648568225, "dur": 1537, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 20696, "tid": 230, "ts": 1751886648598986, "dur": 2690, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 20696, "tid": 230, "ts": 1751886648601739, "dur": 4529, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 20696, "tid": 230, "ts": 1751886648592862, "dur": 14964, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}