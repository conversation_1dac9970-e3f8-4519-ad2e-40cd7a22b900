{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 22320, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 22320, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 22320, "tid": 687, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 22320, "tid": 687, "ts": 1751900030254906, "dur": 32, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 22320, "tid": 687, "ts": 1751900030254950, "dur": 4, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 22320, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 22320, "tid": 1, "ts": 1751900029866383, "dur": 3291, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 22320, "tid": 1, "ts": 1751900029869680, "dur": 38628, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 22320, "tid": 1, "ts": 1751900029908310, "dur": 30780, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 22320, "tid": 687, "ts": 1751900030254956, "dur": 10, "ph": "X", "name": "", "args": {}}, {"pid": 22320, "tid": 25769803776, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029866345, "dur": 17659, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029884005, "dur": 370363, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029884016, "dur": 46, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029884065, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029884067, "dur": 30799, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029914876, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029914881, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029914942, "dur": 15, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029914959, "dur": 1000, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029915964, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029915968, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029916028, "dur": 2, "ph": "X", "name": "ProcessMessages 730", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029916032, "dur": 53, "ph": "X", "name": "ReadAsync 730", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029916087, "dur": 2, "ph": "X", "name": "ProcessMessages 654", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029916091, "dur": 57, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029916151, "dur": 1, "ph": "X", "name": "ProcessMessages 554", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029916154, "dur": 49, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029916207, "dur": 2, "ph": "X", "name": "ProcessMessages 568", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029916210, "dur": 63, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029916277, "dur": 1, "ph": "X", "name": "ProcessMessages 598", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029916280, "dur": 64, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029916347, "dur": 1, "ph": "X", "name": "ProcessMessages 875", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029916350, "dur": 45, "ph": "X", "name": "ReadAsync 875", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029916397, "dur": 1, "ph": "X", "name": "ProcessMessages 643", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029916400, "dur": 80, "ph": "X", "name": "ReadAsync 643", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029916484, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029916486, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029916548, "dur": 1, "ph": "X", "name": "ProcessMessages 718", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029916550, "dur": 45, "ph": "X", "name": "ReadAsync 718", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029916599, "dur": 1, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029916602, "dur": 45, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029916648, "dur": 1, "ph": "X", "name": "ProcessMessages 468", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029916650, "dur": 45, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029916697, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029916699, "dur": 40, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029916741, "dur": 1, "ph": "X", "name": "ProcessMessages 603", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029916743, "dur": 36, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029916781, "dur": 1, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029916783, "dur": 52, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029916839, "dur": 1, "ph": "X", "name": "ProcessMessages 425", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029916841, "dur": 43, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029916886, "dur": 1, "ph": "X", "name": "ProcessMessages 652", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029916889, "dur": 38, "ph": "X", "name": "ReadAsync 652", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029916928, "dur": 1, "ph": "X", "name": "ProcessMessages 600", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029916930, "dur": 30, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029916964, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029917002, "dur": 34, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029917038, "dur": 1, "ph": "X", "name": "ProcessMessages 437", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029917047, "dur": 35, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029917084, "dur": 1, "ph": "X", "name": "ProcessMessages 470", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029917086, "dur": 37, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029917126, "dur": 1, "ph": "X", "name": "ProcessMessages 436", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029917128, "dur": 37, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029917167, "dur": 1, "ph": "X", "name": "ProcessMessages 488", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029917169, "dur": 36, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029917208, "dur": 32, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029917249, "dur": 1, "ph": "X", "name": "ProcessMessages 335", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029917250, "dur": 39, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029917292, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029917293, "dur": 25, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029917321, "dur": 29, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029917354, "dur": 66, "ph": "X", "name": "ReadAsync 59", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029917422, "dur": 1, "ph": "X", "name": "ProcessMessages 598", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029917425, "dur": 46, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029917474, "dur": 1, "ph": "X", "name": "ProcessMessages 822", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029917476, "dur": 51, "ph": "X", "name": "ReadAsync 822", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029917530, "dur": 1, "ph": "X", "name": "ProcessMessages 589", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029917532, "dur": 45, "ph": "X", "name": "ReadAsync 589", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029917579, "dur": 1, "ph": "X", "name": "ProcessMessages 503", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029917581, "dur": 43, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029917626, "dur": 1, "ph": "X", "name": "ProcessMessages 589", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029917628, "dur": 38, "ph": "X", "name": "ReadAsync 589", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029917669, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029917671, "dur": 37, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029917710, "dur": 1, "ph": "X", "name": "ProcessMessages 521", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029917712, "dur": 35, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029917749, "dur": 1, "ph": "X", "name": "ProcessMessages 389", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029917751, "dur": 36, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029917789, "dur": 1, "ph": "X", "name": "ProcessMessages 541", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029917790, "dur": 37, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029917829, "dur": 1, "ph": "X", "name": "ProcessMessages 337", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029917830, "dur": 39, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029917872, "dur": 1, "ph": "X", "name": "ProcessMessages 555", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029917873, "dur": 30, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029917907, "dur": 33, "ph": "X", "name": "ReadAsync 49", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029917943, "dur": 32, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029917977, "dur": 1, "ph": "X", "name": "ProcessMessages 350", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029917978, "dur": 38, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029918018, "dur": 1, "ph": "X", "name": "ProcessMessages 399", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029918020, "dur": 38, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029918060, "dur": 1, "ph": "X", "name": "ProcessMessages 453", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029918061, "dur": 37, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029918102, "dur": 34, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029918138, "dur": 1, "ph": "X", "name": "ProcessMessages 362", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029918139, "dur": 38, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029918179, "dur": 1, "ph": "X", "name": "ProcessMessages 432", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029918182, "dur": 37, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029918221, "dur": 1, "ph": "X", "name": "ProcessMessages 486", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029918223, "dur": 35, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029918261, "dur": 34, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029918296, "dur": 1, "ph": "X", "name": "ProcessMessages 345", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029918298, "dur": 37, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029918337, "dur": 1, "ph": "X", "name": "ProcessMessages 410", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029918339, "dur": 35, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029918378, "dur": 71, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029918452, "dur": 1, "ph": "X", "name": "ProcessMessages 569", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029918454, "dur": 59, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029918518, "dur": 1, "ph": "X", "name": "ProcessMessages 492", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029918520, "dur": 59, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029918583, "dur": 1, "ph": "X", "name": "ProcessMessages 759", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029918586, "dur": 41, "ph": "X", "name": "ReadAsync 759", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029918628, "dur": 1, "ph": "X", "name": "ProcessMessages 853", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029918630, "dur": 49, "ph": "X", "name": "ReadAsync 853", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029918682, "dur": 1, "ph": "X", "name": "ProcessMessages 345", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029918684, "dur": 56, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029918743, "dur": 1, "ph": "X", "name": "ProcessMessages 682", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029918746, "dur": 57, "ph": "X", "name": "ReadAsync 682", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029918806, "dur": 1, "ph": "X", "name": "ProcessMessages 787", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029918809, "dur": 53, "ph": "X", "name": "ReadAsync 787", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029918865, "dur": 1, "ph": "X", "name": "ProcessMessages 627", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029918867, "dur": 56, "ph": "X", "name": "ReadAsync 627", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029918926, "dur": 2, "ph": "X", "name": "ProcessMessages 873", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029918929, "dur": 56, "ph": "X", "name": "ReadAsync 873", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029918988, "dur": 1, "ph": "X", "name": "ProcessMessages 650", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029918991, "dur": 56, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029919049, "dur": 2, "ph": "X", "name": "ProcessMessages 807", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029919052, "dur": 56, "ph": "X", "name": "ReadAsync 807", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029919111, "dur": 1, "ph": "X", "name": "ProcessMessages 767", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029919114, "dur": 55, "ph": "X", "name": "ReadAsync 767", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029919171, "dur": 2, "ph": "X", "name": "ProcessMessages 643", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029919174, "dur": 55, "ph": "X", "name": "ReadAsync 643", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029919232, "dur": 1, "ph": "X", "name": "ProcessMessages 815", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029919234, "dur": 54, "ph": "X", "name": "ReadAsync 815", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029919291, "dur": 1, "ph": "X", "name": "ProcessMessages 391", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029919293, "dur": 56, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029919352, "dur": 1, "ph": "X", "name": "ProcessMessages 772", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029919354, "dur": 49, "ph": "X", "name": "ReadAsync 772", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029919406, "dur": 1, "ph": "X", "name": "ProcessMessages 716", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029919409, "dur": 39, "ph": "X", "name": "ReadAsync 716", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029919450, "dur": 1, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029919451, "dur": 35, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029919490, "dur": 34, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029919526, "dur": 1, "ph": "X", "name": "ProcessMessages 365", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029919527, "dur": 31, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029919561, "dur": 35, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029919600, "dur": 34, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029919637, "dur": 35, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029919675, "dur": 31, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029919707, "dur": 1, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029919708, "dur": 35, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029919746, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029919780, "dur": 29, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029919811, "dur": 35, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029919848, "dur": 1, "ph": "X", "name": "ProcessMessages 345", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029919849, "dur": 33, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029919885, "dur": 35, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029919923, "dur": 35, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029919959, "dur": 1, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029919962, "dur": 34, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029920001, "dur": 33, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029920037, "dur": 21, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029920062, "dur": 65, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029920130, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029920168, "dur": 35, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029920206, "dur": 31, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029920239, "dur": 1, "ph": "X", "name": "ProcessMessages 299", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029920241, "dur": 38, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029920280, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029920281, "dur": 34, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029920318, "dur": 43, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029920365, "dur": 40, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029920407, "dur": 1, "ph": "X", "name": "ProcessMessages 474", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029920409, "dur": 37, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029920448, "dur": 1, "ph": "X", "name": "ProcessMessages 397", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029920449, "dur": 35, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029920488, "dur": 34, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029920525, "dur": 21, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029920549, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029920585, "dur": 36, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029920624, "dur": 33, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029920659, "dur": 1, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029920661, "dur": 30, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029920696, "dur": 34, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029920732, "dur": 27, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029920762, "dur": 31, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029920796, "dur": 34, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029920833, "dur": 34, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029920870, "dur": 34, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029920905, "dur": 1, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029920907, "dur": 33, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029920943, "dur": 34, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029920979, "dur": 1, "ph": "X", "name": "ProcessMessages 317", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029920981, "dur": 25, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029921009, "dur": 31, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029921043, "dur": 34, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029921081, "dur": 34, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029921118, "dur": 31, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029921150, "dur": 1, "ph": "X", "name": "ProcessMessages 307", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029921151, "dur": 34, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029921188, "dur": 29, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029921227, "dur": 36, "ph": "X", "name": "ReadAsync 184", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029921266, "dur": 33, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029921302, "dur": 33, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029921337, "dur": 1, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029921339, "dur": 34, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029921375, "dur": 33, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029921412, "dur": 27, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029921442, "dur": 36, "ph": "X", "name": "ReadAsync 69", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029921481, "dur": 34, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029921519, "dur": 34, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029921555, "dur": 1, "ph": "X", "name": "ProcessMessages 447", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029921556, "dur": 30, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029921589, "dur": 34, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029921626, "dur": 29, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029921659, "dur": 33, "ph": "X", "name": "ReadAsync 223", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029921695, "dur": 33, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029921731, "dur": 34, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029921768, "dur": 33, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029921805, "dur": 42, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029921850, "dur": 2, "ph": "X", "name": "ProcessMessages 506", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029921853, "dur": 45, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029921901, "dur": 1, "ph": "X", "name": "ProcessMessages 249", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029921903, "dur": 45, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029921951, "dur": 1, "ph": "X", "name": "ProcessMessages 584", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029921952, "dur": 34, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029921989, "dur": 1, "ph": "X", "name": "ProcessMessages 309", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029921990, "dur": 36, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029922030, "dur": 33, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029922067, "dur": 35, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029922105, "dur": 27, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029922135, "dur": 35, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029922174, "dur": 37, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029922213, "dur": 1, "ph": "X", "name": "ProcessMessages 424", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029922215, "dur": 39, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029922258, "dur": 1, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029922262, "dur": 43, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029922307, "dur": 1, "ph": "X", "name": "ProcessMessages 297", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029922310, "dur": 44, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029922359, "dur": 33, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029922395, "dur": 1, "ph": "X", "name": "ProcessMessages 72", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029922396, "dur": 38, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029922438, "dur": 35, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029922476, "dur": 31, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029922511, "dur": 35, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029922549, "dur": 35, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029922587, "dur": 33, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029922623, "dur": 31, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029922657, "dur": 40, "ph": "X", "name": "ReadAsync 174", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029922699, "dur": 1, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029922701, "dur": 46, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029922749, "dur": 1, "ph": "X", "name": "ProcessMessages 719", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029922751, "dur": 33, "ph": "X", "name": "ReadAsync 719", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029922787, "dur": 36, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029922826, "dur": 34, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029922864, "dur": 29, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029922896, "dur": 173, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029923072, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029923110, "dur": 34, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029923147, "dur": 1, "ph": "X", "name": "ProcessMessages 266", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029923148, "dur": 34, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029923185, "dur": 1, "ph": "X", "name": "ProcessMessages 243", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029923186, "dur": 35, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029923223, "dur": 1, "ph": "X", "name": "ProcessMessages 430", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029923225, "dur": 32, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029923259, "dur": 37, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029923299, "dur": 1, "ph": "X", "name": "ProcessMessages 337", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029923300, "dur": 29, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029923332, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029923334, "dur": 44, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029923382, "dur": 73, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029923458, "dur": 1, "ph": "X", "name": "ProcessMessages 345", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029923460, "dur": 40, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029923503, "dur": 1, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029923505, "dur": 38, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029923544, "dur": 1, "ph": "X", "name": "ProcessMessages 506", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029923546, "dur": 37, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029923586, "dur": 39, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029923627, "dur": 1, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029923629, "dur": 33, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029923666, "dur": 35, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029923704, "dur": 1, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029923706, "dur": 28, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029923739, "dur": 52, "ph": "X", "name": "ReadAsync 131", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029923794, "dur": 1, "ph": "X", "name": "ProcessMessages 471", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029923796, "dur": 47, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029923846, "dur": 1, "ph": "X", "name": "ProcessMessages 679", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029923849, "dur": 34, "ph": "X", "name": "ReadAsync 679", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029923887, "dur": 1, "ph": "X", "name": "ProcessMessages 180", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029923889, "dur": 56, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029923948, "dur": 1, "ph": "X", "name": "ProcessMessages 624", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029923950, "dur": 53, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029924006, "dur": 2, "ph": "X", "name": "ProcessMessages 837", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029924009, "dur": 46, "ph": "X", "name": "ReadAsync 837", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029924058, "dur": 1, "ph": "X", "name": "ProcessMessages 510", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029924061, "dur": 30, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029924095, "dur": 43, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029924141, "dur": 1, "ph": "X", "name": "ProcessMessages 516", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029924143, "dur": 40, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029924186, "dur": 1, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029924188, "dur": 39, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029924229, "dur": 1, "ph": "X", "name": "ProcessMessages 476", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029924231, "dur": 40, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029924274, "dur": 31, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029924306, "dur": 1, "ph": "X", "name": "ProcessMessages 361", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029924308, "dur": 31, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029924343, "dur": 34, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029924379, "dur": 1, "ph": "X", "name": "ProcessMessages 350", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029924380, "dur": 37, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029924420, "dur": 35, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029924456, "dur": 2, "ph": "X", "name": "ProcessMessages 333", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029924459, "dur": 36, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029924498, "dur": 29, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029924529, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029924531, "dur": 33, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029924567, "dur": 30, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029924599, "dur": 36, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029924637, "dur": 1, "ph": "X", "name": "ProcessMessages 558", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029924640, "dur": 33, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029924675, "dur": 1, "ph": "X", "name": "ProcessMessages 354", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029924676, "dur": 33, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029924712, "dur": 28, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029924743, "dur": 30, "ph": "X", "name": "ReadAsync 184", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029924776, "dur": 37, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029924815, "dur": 1, "ph": "X", "name": "ProcessMessages 672", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029924817, "dur": 33, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029924852, "dur": 1, "ph": "X", "name": "ProcessMessages 416", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029924853, "dur": 33, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029924890, "dur": 34, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029924928, "dur": 33, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029924963, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029924965, "dur": 36, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029925004, "dur": 1, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029925006, "dur": 40, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029925049, "dur": 1, "ph": "X", "name": "ProcessMessages 654", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029925051, "dur": 42, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029925096, "dur": 35, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029925133, "dur": 1, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029925135, "dur": 31, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029925169, "dur": 1, "ph": "X", "name": "ProcessMessages 153", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029925171, "dur": 39, "ph": "X", "name": "ReadAsync 153", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029925212, "dur": 1, "ph": "X", "name": "ProcessMessages 550", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029925213, "dur": 35, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029925250, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029925251, "dur": 34, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029925288, "dur": 36, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029925328, "dur": 33, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029925364, "dur": 1, "ph": "X", "name": "ProcessMessages 373", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029925366, "dur": 56, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029925425, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029925427, "dur": 51, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029925481, "dur": 1, "ph": "X", "name": "ProcessMessages 852", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029925484, "dur": 53, "ph": "X", "name": "ReadAsync 852", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029925540, "dur": 1, "ph": "X", "name": "ProcessMessages 375", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029925542, "dur": 39, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029925584, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029925585, "dur": 34, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029925621, "dur": 1, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029925623, "dur": 35, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029925661, "dur": 35, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029925700, "dur": 35, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029925737, "dur": 1, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029925739, "dur": 36, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029925778, "dur": 28, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029925809, "dur": 31, "ph": "X", "name": "ReadAsync 78", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029925843, "dur": 33, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029925879, "dur": 3848, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029929734, "dur": 2, "ph": "X", "name": "ProcessMessages 557", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029929737, "dur": 361, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029930101, "dur": 18, "ph": "X", "name": "ProcessMessages 20543", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029930121, "dur": 38, "ph": "X", "name": "ReadAsync 20543", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029930161, "dur": 1, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029930163, "dur": 34, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029930199, "dur": 39, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029930241, "dur": 1, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029930243, "dur": 34, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029930279, "dur": 9, "ph": "X", "name": "ProcessMessages 219", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029930291, "dur": 37, "ph": "X", "name": "ReadAsync 219", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029930330, "dur": 1, "ph": "X", "name": "ProcessMessages 657", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029930331, "dur": 44, "ph": "X", "name": "ReadAsync 657", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029930384, "dur": 1, "ph": "X", "name": "ProcessMessages 421", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029930386, "dur": 43, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029930435, "dur": 1, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029930438, "dur": 40, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029930481, "dur": 1, "ph": "X", "name": "ProcessMessages 558", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029930483, "dur": 37, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029930524, "dur": 1, "ph": "X", "name": "ProcessMessages 424", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029930525, "dur": 35, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029930562, "dur": 1, "ph": "X", "name": "ProcessMessages 430", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029930563, "dur": 120, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029930689, "dur": 2, "ph": "X", "name": "ProcessMessages 1399", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029930693, "dur": 38, "ph": "X", "name": "ReadAsync 1399", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029930734, "dur": 10, "ph": "X", "name": "ProcessMessages 597", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029930744, "dur": 39, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029930785, "dur": 1, "ph": "X", "name": "ProcessMessages 739", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029930787, "dur": 37, "ph": "X", "name": "ReadAsync 739", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029930827, "dur": 36, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029930868, "dur": 1, "ph": "X", "name": "ProcessMessages 358", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029930870, "dur": 38, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029930910, "dur": 1, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029930912, "dur": 49, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029930969, "dur": 1, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029930971, "dur": 39, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029931014, "dur": 1, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029931017, "dur": 37, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029931057, "dur": 1, "ph": "X", "name": "ProcessMessages 215", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029931059, "dur": 190, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029931253, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029931255, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029931295, "dur": 2, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029931298, "dur": 33, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029931341, "dur": 2, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029931344, "dur": 37, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029931385, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029931389, "dur": 33, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029931425, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029931428, "dur": 32, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029931463, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029931465, "dur": 29, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029931496, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029931498, "dur": 29, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029931532, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029931567, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029931569, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029931603, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029931606, "dur": 33, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029931642, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029931644, "dur": 32, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029931678, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029931680, "dur": 37, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029931720, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029931723, "dur": 32, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029931757, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029931759, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029931795, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029931798, "dur": 30, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029931837, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029931839, "dur": 33, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029931875, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029931877, "dur": 55, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029931934, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029931936, "dur": 36, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029931975, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029931987, "dur": 31, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029932020, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029932022, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029932059, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029932061, "dur": 36, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029932100, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029932102, "dur": 31, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029932136, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029932139, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029932176, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029932182, "dur": 35, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029932224, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029932228, "dur": 35, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029932266, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029932268, "dur": 37, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029932308, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029932310, "dur": 30, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029932343, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029932345, "dur": 43, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029932391, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029932393, "dur": 31, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029932430, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029932471, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029932474, "dur": 37, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029932640, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029932643, "dur": 42, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029932687, "dur": 3, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029932692, "dur": 32, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029932727, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029932730, "dur": 34, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029932767, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029932770, "dur": 37, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029932810, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029932812, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029932853, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029932856, "dur": 37, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029932897, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029932899, "dur": 32, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029932935, "dur": 2, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029932938, "dur": 43, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029932984, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029932987, "dur": 40, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029933031, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029933033, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029933063, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029933065, "dur": 27, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029933095, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029933099, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029933136, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029933139, "dur": 36, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029933182, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029933185, "dur": 35, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029933223, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029933226, "dur": 44, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029933273, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029933275, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029933324, "dur": 2, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029933328, "dur": 38, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029933369, "dur": 2, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029933373, "dur": 35, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029933412, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029933414, "dur": 40, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029933457, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029933460, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029933498, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029933500, "dur": 36, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029933540, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029933542, "dur": 33, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029933578, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029933580, "dur": 31, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029933614, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029933618, "dur": 35, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029933655, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029933658, "dur": 3209, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029936872, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029936875, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029936914, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029936917, "dur": 42, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029936962, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029936964, "dur": 988, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029937955, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029937958, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029937995, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029937998, "dur": 53, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029938055, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029938091, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029938093, "dur": 72, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029938169, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029938200, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029938203, "dur": 3165, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029941374, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029941378, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029941419, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029941422, "dur": 94, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029941520, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029941549, "dur": 397, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029941950, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029941953, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029941998, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029942001, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029942037, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029942039, "dur": 164, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029942206, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029942208, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029942243, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029942245, "dur": 29, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029942276, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029942279, "dur": 125, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029942408, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029942436, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029942438, "dur": 312, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029942755, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029942787, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029942789, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029942822, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029942824, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029942855, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029942857, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029942895, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029942924, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029942927, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029942957, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029942984, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029942986, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029943017, "dur": 88, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029943109, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029943137, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029943170, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029943172, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029943204, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029943206, "dur": 36, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029943246, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029943273, "dur": 64, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029943341, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029943371, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029943418, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029943446, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029943448, "dur": 188, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029943640, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029943674, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029943702, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029943729, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029943731, "dur": 137, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029943873, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029943903, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029943905, "dur": 27, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029943935, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029943971, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029943973, "dur": 73, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029944051, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029944099, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029944128, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029944130, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029944161, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029944164, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029944196, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029944235, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029944261, "dur": 76, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029944341, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029944374, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029944376, "dur": 51, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029944432, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029944467, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029944469, "dur": 34, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029944506, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029944508, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029944539, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029944541, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029944579, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029944581, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029944620, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029944622, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029944660, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029944663, "dur": 32, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029944697, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029944699, "dur": 30, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029944732, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029944735, "dur": 30, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029944767, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029944769, "dur": 25, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029944798, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029944830, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029944833, "dur": 29, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029944865, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029944867, "dur": 30, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029944899, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029944902, "dur": 45, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029944949, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029944952, "dur": 82, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029945038, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029945078, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029945080, "dur": 42, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029945125, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029945127, "dur": 34, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029945164, "dur": 102, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029945272, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029945311, "dur": 44, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029945359, "dur": 94, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029945458, "dur": 87, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029945549, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029945550, "dur": 612, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029946166, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029946168, "dur": 85, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029946256, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029946259, "dur": 33, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029946296, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029946340, "dur": 106, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029946450, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029946489, "dur": 130, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029946624, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029946662, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029946664, "dur": 32, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029946700, "dur": 192, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029946897, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029946937, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029946971, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029946972, "dur": 67, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029947043, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029947072, "dur": 385, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029947462, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029947499, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029947502, "dur": 39, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029947545, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029947547, "dur": 82, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029947634, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029947673, "dur": 215, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029947892, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029947894, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029947932, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029947934, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029947966, "dur": 65, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029948035, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029948067, "dur": 672, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029948745, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029948750, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029948796, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900029948800, "dur": 221836, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900030170645, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900030170648, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900030170792, "dur": 47, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900030170843, "dur": 23, "ph": "X", "name": "ProcessMessages 387", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900030170868, "dur": 1801, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900030172675, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900030172680, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900030172728, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900030172734, "dur": 2409, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900030175174, "dur": 8, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900030175186, "dur": 174, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900030175373, "dur": 71, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900030175448, "dur": 71679, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900030247134, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900030247138, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900030247190, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900030247195, "dur": 832, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900030248037, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900030248040, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900030248098, "dur": 27, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900030248127, "dur": 415, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900030248547, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900030248550, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900030248603, "dur": 1, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 22320, "tid": 25769803776, "ts": 1751900030248606, "dur": 5757, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 22320, "tid": 687, "ts": 1751900030254969, "dur": 1850, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 22320, "tid": 21474836480, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 22320, "tid": 21474836480, "ts": 1751900029866256, "dur": 72844, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751900029939101, "dur": 1, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 22320, "tid": 21474836480, "ts": 1751900029939102, "dur": 51, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 22320, "tid": 687, "ts": 1751900030256823, "dur": 10, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 22320, "tid": 17179869184, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 22320, "tid": 17179869184, "ts": 1751900029861985, "dur": 392422, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 22320, "tid": 17179869184, "ts": 1751900029862103, "dur": 3481, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 22320, "tid": 17179869184, "ts": 1751900030254412, "dur": 55, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 22320, "tid": 17179869184, "ts": 1751900030254424, "dur": 18, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 22320, "tid": 17179869184, "ts": 1751900030254469, "dur": 1, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 22320, "tid": 687, "ts": 1751900030256836, "dur": 10, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1751900029883682, "dur": 30962, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751900029914666, "dur": 465, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751900029915191, "dur": 50, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1751900029915242, "dur": 317, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751900029918057, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_1D34F993682102CF.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751900029923058, "dur": 80, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751900029926962, "dur": 2813, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751900029915589, "dur": 15112, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751900029930712, "dur": 317003, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751900030247718, "dur": 117, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751900030247856, "dur": 108, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751900030247989, "dur": 50, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751900030248078, "dur": 93, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751900030248193, "dur": 1630, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1751900029915987, "dur": 14739, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900029930739, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_2784FA9BFF134207.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751900029931213, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_B7F547A15B2C01B2.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751900029931349, "dur": 253, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_A7F411FD3CF1CCBD.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751900029931605, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_B9448EEF9FF8FB2A.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751900029931741, "dur": 121, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_177F4FE55DE2E02C.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751900029932060, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900029932361, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751900029932591, "dur": 174, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751900029932853, "dur": 362, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1751900029933288, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900029933542, "dur": 391, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900029933934, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900029934176, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900029934391, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900029934605, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900029934830, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900029935061, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900029935304, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900029935539, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900029935755, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900029935979, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900029936198, "dur": 578, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900029936777, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900029936987, "dur": 605, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900029937592, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900029937856, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900029938299, "dur": 1267, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui\\Runtime\\TMP\\TMP_Text.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751900029938068, "dur": 1521, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900029939589, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900029939811, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900029940007, "dur": 119, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900029940126, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900029940365, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900029940717, "dur": 326, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900029941043, "dur": 578, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900029941650, "dur": 68, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900029941719, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751900029941906, "dur": 591, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751900029942497, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900029942656, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751900029942832, "dur": 1176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751900029944009, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900029944499, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1751900029944582, "dur": 187, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900029944788, "dur": 185, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900029944974, "dur": 889, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900029945864, "dur": 73, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900029945968, "dur": 346, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900029946314, "dur": 255, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900029946591, "dur": 538, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900029947161, "dur": 402, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900029947590, "dur": 300131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900029916025, "dur": 14717, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900029930747, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_7359E91D15751A95.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751900029930950, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_6BC1B44B0B58CEF0.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751900029931321, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_42529D1BD0B2CBE0.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751900029931554, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_9D0087D0CAC25D2C.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751900029931870, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900029932044, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1751900029932376, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751900029932594, "dur": 119, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1751900029933160, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900029933298, "dur": 547, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900029933846, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900029934061, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900029934314, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900029934519, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900029934753, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900029935183, "dur": 1039, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Flow\\Ports\\IUnitOutputPort.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751900029934991, "dur": 1245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900029936236, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900029936459, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900029936700, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900029936929, "dur": 587, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900029937517, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900029937791, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900029938035, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900029938272, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900029938492, "dur": 458, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900029938950, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900029939160, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900029939364, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900029939590, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900029939798, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900029940004, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900029940215, "dur": 458, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900029940712, "dur": 330, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900029941042, "dur": 581, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900029941624, "dur": 59, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900029941684, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751900029941869, "dur": 579, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751900029942560, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751900029942788, "dur": 878, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751900029943704, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751900029943776, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900029943903, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751900029944113, "dur": 563, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751900029944734, "dur": 213, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900029944947, "dur": 894, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900029945841, "dur": 99, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900029945940, "dur": 347, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900029946311, "dur": 259, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900029946599, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751900029946707, "dur": 351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751900029947058, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900029947165, "dur": 403, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900029947568, "dur": 300133, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900029916199, "dur": 14609, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900029930811, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_2C2D2F6DBE48DE8F.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751900029930974, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_91F787F420E8C88E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751900029931070, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900029931188, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_DF7681D3753AB490.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751900029931544, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_5E3C6CF57067B635.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751900029931616, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_7882E8644625FF23.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751900029931841, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_83F4E4F0FA017AFD.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751900029931999, "dur": 147, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_83F4E4F0FA017AFD.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751900029932184, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900029932281, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751900029932352, "dur": 132, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751900029932692, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900029932986, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751900029933267, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900029933517, "dur": 447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900029933964, "dur": 285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900029934250, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900029934467, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900029934686, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900029934923, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900029935126, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900029935400, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900029935619, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900029935845, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900029936066, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900029936282, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900029936502, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900029936774, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900029936985, "dur": 605, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900029937590, "dur": 347, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900029937937, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900029938148, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900029938356, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900029938564, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900029939701, "dur": 297, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "E:/Unity/Editior/6000.0.34f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 3, "ts": 1751900029938835, "dur": 1163, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900029939999, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900029940249, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900029940507, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900029940774, "dur": 272, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900029941047, "dur": 602, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900029941679, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751900029941873, "dur": 498, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751900029942438, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751900029942618, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751900029942809, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900029942910, "dur": 523, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751900029943433, "dur": 371, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900029943830, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751900029944007, "dur": 649, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751900029944737, "dur": 203, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900029944971, "dur": 865, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900029945859, "dur": 80, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900029945939, "dur": 355, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900029946294, "dur": 279, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900029946573, "dur": 565, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900029947168, "dur": 396, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900029947593, "dur": 300091, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900029916092, "dur": 14672, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900029930769, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_AAC1DDBB583EF4F7.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751900029930870, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900029931032, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900029931123, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900029931305, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_13694A2D45790405.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751900029931481, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_C1FAB9A8897A5958.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751900029931580, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_40A52831373DC9B0.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751900029931799, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_D380F5A76DBED59A.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751900029932021, "dur": 361, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_D380F5A76DBED59A.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751900029932391, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751900029932552, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751900029932616, "dur": 4949, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751900029937639, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751900029937832, "dur": 3149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751900029941062, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751900029941183, "dur": 387, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751900029941690, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751900029941888, "dur": 1359, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751900029943346, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751900029943536, "dur": 1357, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751900029944964, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751900029945074, "dur": 716, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751900029945854, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751900029945970, "dur": 276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751900029946305, "dur": 266, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900029946604, "dur": 524, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900029947175, "dur": 395, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900029947570, "dur": 300110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900029916079, "dur": 14674, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900029930759, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_0E44AF698B21F99B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751900029931037, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900029931271, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_6A35B4E7D99F65C8.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751900029931497, "dur": 127, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_D397B83A68A481D1.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751900029931626, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_FB81041CC44652D8.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751900029932230, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900029932889, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1751900029933205, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751900029933305, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900029933578, "dur": 416, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900029933995, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900029934217, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900029934435, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900029934649, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900029934869, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900029935084, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900029935358, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900029935585, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900029935814, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900029936028, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900029936240, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900029936456, "dur": 371, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900029936827, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900029937044, "dur": 655, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900029937723, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900029937914, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900029938108, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900029938317, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900029938526, "dur": 338, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900029938864, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900029939078, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900029939296, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900029939511, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900029939716, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900029939908, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900029940139, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900029940394, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900029940715, "dur": 324, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900029941065, "dur": 563, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900029941683, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751900029941876, "dur": 517, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751900029942394, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900029942614, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751900029942839, "dur": 584, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751900029943424, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900029943555, "dur": 651, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751900029944207, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900029944574, "dur": 125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900029944720, "dur": 230, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900029944950, "dur": 914, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900029945864, "dur": 80, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900029945944, "dur": 371, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900029946315, "dur": 272, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900029946587, "dur": 540, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900029947166, "dur": 401, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900029947568, "dur": 300143, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900029916130, "dur": 14658, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900029930795, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_AD3F2B6EFF130063.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751900029931269, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900029931574, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900029931739, "dur": 118, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_A44562DC2280D3AD.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751900029931964, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_F04876DA9D7398DA.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751900029932051, "dur": 167, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1751900029932230, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900029932350, "dur": 119, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751900029932551, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1751900029932630, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751900029933200, "dur": 94, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1751900029933307, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900029933576, "dur": 390, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900029933966, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900029934218, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900029934436, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900029934653, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900029934889, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900029935104, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900029935365, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900029935598, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900029935820, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900029936031, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900029936276, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900029936505, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900029936719, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900029936938, "dur": 604, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900029937542, "dur": 425, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900029937967, "dur": 365, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900029938332, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900029938537, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900029938756, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900029938973, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900029939182, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900029939378, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900029939618, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900029939835, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900029940113, "dur": 630, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900029940743, "dur": 325, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900029941068, "dur": 583, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900029941651, "dur": 68, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900029941720, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751900029941918, "dur": 503, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751900029942480, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751900029942644, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751900029942869, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900029943006, "dur": 556, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751900029943604, "dur": 861, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751900029944591, "dur": 351, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900029944968, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751900029945077, "dur": 814, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751900029945957, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751900029946072, "dur": 448, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751900029946598, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751900029946706, "dur": 372, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751900029947187, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751900029947291, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751900029947583, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751900029947695, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751900029948477, "dur": 221796, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751900030171923, "dur": 74709, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751900030171913, "dur": 74721, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751900030246671, "dur": 976, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751900029916185, "dur": 14617, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900029930806, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_31DB0EF841A9843D.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751900029930993, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_111B28F0FC0CD3DB.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751900029931233, "dur": 125, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_111B28F0FC0CD3DB.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751900029931370, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_656AB5FDB0417D9D.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751900029931582, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_1D503C7A8290C7BE.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751900029931691, "dur": 137, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_1D503C7A8290C7BE.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751900029932226, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751900029932395, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751900029932569, "dur": 3909, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751900029936584, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900029936820, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900029937034, "dur": 723, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900029937758, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900029937973, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900029938230, "dur": 675, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui\\Runtime\\TMP\\FontFeatureCommon.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751900029938169, "dur": 866, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900029939035, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900029939234, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900029939437, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900029939691, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900029939896, "dur": 612, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900029940509, "dur": 114, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900029940713, "dur": 354, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900029941068, "dur": 574, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900029941681, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751900029941858, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900029942088, "dur": 649, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751900029942857, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751900029943083, "dur": 520, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751900029943604, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900029943833, "dur": 252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751900029944117, "dur": 605, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751900029944770, "dur": 175, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900029944969, "dur": 868, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900029945837, "dur": 107, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900029945944, "dur": 345, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900029946289, "dur": 285, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900029946574, "dur": 563, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900029947177, "dur": 395, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900029947572, "dur": 300111, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900029916251, "dur": 14572, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900029930869, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900029930981, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_BD8ABEB80D593DCC.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751900029931215, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_1AA34DA22D3674A5.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751900029931576, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_5BB3CA76865503EA.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751900029931995, "dur": 249, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1751900029932308, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751900029932742, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900029932890, "dur": 223, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751900029933269, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900029933517, "dur": 385, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900029933903, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900029934110, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900029934332, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900029934517, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900029934703, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900029934917, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900029935108, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900029935330, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900029935533, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900029935720, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900029935909, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900029936091, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900029936306, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900029936500, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900029936688, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900029936880, "dur": 552, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900029937432, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900029937750, "dur": 568, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Ensure\\EnsureThat.Guids.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751900029937718, "dur": 791, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900029938509, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900029938723, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900029938954, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900029939175, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900029939381, "dur": 550, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900029939931, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900029940146, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900029940364, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900029940715, "dur": 321, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900029941063, "dur": 559, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900029941747, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751900029941935, "dur": 1290, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751900029943225, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900029943392, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751900029943563, "dur": 456, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751900029944020, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900029944093, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.InputSystem.ForUI.ref.dll_0478B67D1094CE70.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751900029944225, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900029944579, "dur": 143, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900029944722, "dur": 251, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900029944973, "dur": 862, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900029945874, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900029945960, "dur": 328, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900029946319, "dur": 258, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900029946577, "dur": 553, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900029947156, "dur": 410, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900029947566, "dur": 224387, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900030171972, "dur": 316, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1751900030171954, "dur": 336, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1751900030172350, "dur": 2331, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1751900030174702, "dur": 73011, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751900030252695, "dur": 951, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 22320, "tid": 687, "ts": 1751900030256903, "dur": 1493, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 22320, "tid": 687, "ts": 1751900030258429, "dur": 7656, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 22320, "tid": 687, "ts": 1751900030254939, "dur": 11181, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}