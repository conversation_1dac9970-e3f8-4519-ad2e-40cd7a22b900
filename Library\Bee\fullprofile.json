{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 22320, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 22320, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 22320, "tid": 12, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 22320, "tid": 12, "ts": 1751895305505815, "dur": 1484, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 22320, "tid": 12, "ts": 1751895305513533, "dur": 1022, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 22320, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 22320, "tid": 1, "ts": 1751895305389395, "dur": 7287, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 22320, "tid": 1, "ts": 1751895305396687, "dur": 55949, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 22320, "tid": 1, "ts": 1751895305452645, "dur": 33800, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 22320, "tid": 12, "ts": 1751895305514560, "dur": 1663, "ph": "X", "name": "", "args": {}}, {"pid": 22320, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305382562, "dur": 9286, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305391850, "dur": 103413, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305392908, "dur": 4353, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305397268, "dur": 1916, "ph": "X", "name": "ProcessMessages 20496", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305399188, "dur": 368, "ph": "X", "name": "ReadAsync 20496", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305399559, "dur": 16, "ph": "X", "name": "ProcessMessages 20525", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305399577, "dur": 97, "ph": "X", "name": "ReadAsync 20525", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305399676, "dur": 1, "ph": "X", "name": "ProcessMessages 823", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305399678, "dur": 95, "ph": "X", "name": "ReadAsync 823", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305399776, "dur": 1, "ph": "X", "name": "ProcessMessages 297", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305399778, "dur": 35, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305399815, "dur": 1, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305399817, "dur": 129, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305399950, "dur": 98, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305400049, "dur": 1, "ph": "X", "name": "ProcessMessages 562", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305400051, "dur": 40, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305400094, "dur": 446, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305400549, "dur": 1, "ph": "X", "name": "ProcessMessages 440", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305400551, "dur": 201, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305400756, "dur": 24, "ph": "X", "name": "ProcessMessages 4201", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305400781, "dur": 44, "ph": "X", "name": "ReadAsync 4201", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305400829, "dur": 1, "ph": "X", "name": "ProcessMessages 1075", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305400831, "dur": 40, "ph": "X", "name": "ReadAsync 1075", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305400876, "dur": 90, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305400969, "dur": 1, "ph": "X", "name": "ProcessMessages 402", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305400971, "dur": 32, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305401007, "dur": 147, "ph": "X", "name": "ReadAsync 111", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305401157, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305401159, "dur": 58, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305401221, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305401223, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305401256, "dur": 1, "ph": "X", "name": "ProcessMessages 248", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305401258, "dur": 56, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305401317, "dur": 45, "ph": "X", "name": "ReadAsync 33", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305401368, "dur": 1, "ph": "X", "name": "ProcessMessages 427", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305401371, "dur": 66, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305401439, "dur": 2, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305401443, "dur": 70, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305401518, "dur": 2, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305401521, "dur": 60, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305401584, "dur": 1, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305401587, "dur": 49, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305401638, "dur": 1, "ph": "X", "name": "ProcessMessages 338", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305401641, "dur": 77, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305401720, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305401722, "dur": 36, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305401762, "dur": 45, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305401810, "dur": 1, "ph": "X", "name": "ProcessMessages 509", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305401813, "dur": 43, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305401860, "dur": 1, "ph": "X", "name": "ProcessMessages 417", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305401862, "dur": 41, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305401906, "dur": 1, "ph": "X", "name": "ProcessMessages 159", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305401908, "dur": 43, "ph": "X", "name": "ReadAsync 159", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305401954, "dur": 1, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305401956, "dur": 105, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305402064, "dur": 1, "ph": "X", "name": "ProcessMessages 486", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305402066, "dur": 34, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305402102, "dur": 1, "ph": "X", "name": "ProcessMessages 797", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305402103, "dur": 32, "ph": "X", "name": "ReadAsync 797", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305402138, "dur": 31, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305402173, "dur": 39, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305402215, "dur": 42, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305402261, "dur": 41, "ph": "X", "name": "ReadAsync 882", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305402303, "dur": 1, "ph": "X", "name": "ProcessMessages 661", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305402306, "dur": 44, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305402351, "dur": 1, "ph": "X", "name": "ProcessMessages 615", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305402353, "dur": 29, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305402385, "dur": 42, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305402430, "dur": 47, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305402479, "dur": 1, "ph": "X", "name": "ProcessMessages 415", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305402481, "dur": 44, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305402527, "dur": 1, "ph": "X", "name": "ProcessMessages 695", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305402529, "dur": 37, "ph": "X", "name": "ReadAsync 695", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305402568, "dur": 1, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305402570, "dur": 40, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305402613, "dur": 36, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305402650, "dur": 1, "ph": "X", "name": "ProcessMessages 489", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305402652, "dur": 33, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305402689, "dur": 32, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305402724, "dur": 29, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305402755, "dur": 1, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305402756, "dur": 27, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305402786, "dur": 34, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305402822, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305402823, "dur": 30, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305402856, "dur": 32, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305402892, "dur": 35, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305402930, "dur": 33, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305402965, "dur": 1, "ph": "X", "name": "ProcessMessages 356", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305402967, "dur": 28, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305402999, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305403034, "dur": 33, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305403071, "dur": 34, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305403107, "dur": 1, "ph": "X", "name": "ProcessMessages 415", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305403108, "dur": 33, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305403145, "dur": 34, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305403182, "dur": 26, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305403211, "dur": 209, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305403423, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305403458, "dur": 32, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305403493, "dur": 31, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305403528, "dur": 34, "ph": "X", "name": "ReadAsync 133", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305403566, "dur": 32, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305403601, "dur": 29, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305403633, "dur": 28, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305403664, "dur": 33, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305403699, "dur": 1, "ph": "X", "name": "ProcessMessages 419", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305403701, "dur": 34, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305403738, "dur": 34, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305403775, "dur": 31, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305403808, "dur": 1, "ph": "X", "name": "ProcessMessages 317", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305403810, "dur": 28, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305403841, "dur": 31, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305403876, "dur": 32, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305403910, "dur": 1, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305403911, "dur": 36, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305403951, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305403983, "dur": 64, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305404050, "dur": 1, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305404053, "dur": 107, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305404164, "dur": 2, "ph": "X", "name": "ProcessMessages 859", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305404168, "dur": 66, "ph": "X", "name": "ReadAsync 859", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305404238, "dur": 1, "ph": "X", "name": "ProcessMessages 694", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305404240, "dur": 62, "ph": "X", "name": "ReadAsync 694", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305404305, "dur": 2, "ph": "X", "name": "ProcessMessages 866", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305404309, "dur": 31, "ph": "X", "name": "ReadAsync 866", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305404389, "dur": 2, "ph": "X", "name": "ProcessMessages 371", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305404392, "dur": 68, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305404464, "dur": 46, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305404514, "dur": 1, "ph": "X", "name": "ProcessMessages 153", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305404516, "dur": 38, "ph": "X", "name": "ReadAsync 153", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305404557, "dur": 1, "ph": "X", "name": "ProcessMessages 177", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305404559, "dur": 55, "ph": "X", "name": "ReadAsync 177", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305404618, "dur": 1, "ph": "X", "name": "ProcessMessages 354", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305404622, "dur": 58, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305404684, "dur": 3, "ph": "X", "name": "ProcessMessages 250", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305404688, "dur": 55, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305404746, "dur": 1, "ph": "X", "name": "ProcessMessages 571", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305404750, "dur": 51, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305404804, "dur": 1, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305404806, "dur": 49, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305404858, "dur": 1, "ph": "X", "name": "ProcessMessages 355", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305404860, "dur": 100, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305404963, "dur": 17, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305404984, "dur": 63, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305405050, "dur": 3, "ph": "X", "name": "ProcessMessages 1214", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305405070, "dur": 56, "ph": "X", "name": "ReadAsync 1214", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305405130, "dur": 1, "ph": "X", "name": "ProcessMessages 232", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305405133, "dur": 79, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305405215, "dur": 1, "ph": "X", "name": "ProcessMessages 289", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305405217, "dur": 48, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305405268, "dur": 1, "ph": "X", "name": "ProcessMessages 314", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305405271, "dur": 31, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305405305, "dur": 1, "ph": "X", "name": "ProcessMessages 362", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305405308, "dur": 41, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305405354, "dur": 93, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305405450, "dur": 1, "ph": "X", "name": "ProcessMessages 294", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305405453, "dur": 74, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305405531, "dur": 1, "ph": "X", "name": "ProcessMessages 356", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305405534, "dur": 58, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305405595, "dur": 2, "ph": "X", "name": "ProcessMessages 613", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305405599, "dur": 62, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305405664, "dur": 1, "ph": "X", "name": "ProcessMessages 1006", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305405666, "dur": 58, "ph": "X", "name": "ReadAsync 1006", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305405727, "dur": 1, "ph": "X", "name": "ProcessMessages 776", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305405730, "dur": 52, "ph": "X", "name": "ReadAsync 776", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305405785, "dur": 1, "ph": "X", "name": "ProcessMessages 611", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305405787, "dur": 54, "ph": "X", "name": "ReadAsync 611", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305405844, "dur": 1, "ph": "X", "name": "ProcessMessages 389", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305405845, "dur": 60, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305405908, "dur": 2, "ph": "X", "name": "ProcessMessages 691", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305405912, "dur": 55, "ph": "X", "name": "ReadAsync 691", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305405969, "dur": 1, "ph": "X", "name": "ProcessMessages 430", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305405971, "dur": 50, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305406024, "dur": 1, "ph": "X", "name": "ProcessMessages 423", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305406026, "dur": 57, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305406086, "dur": 1, "ph": "X", "name": "ProcessMessages 838", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305406088, "dur": 60, "ph": "X", "name": "ReadAsync 838", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305406151, "dur": 1, "ph": "X", "name": "ProcessMessages 737", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305406153, "dur": 54, "ph": "X", "name": "ReadAsync 737", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305406210, "dur": 1, "ph": "X", "name": "ProcessMessages 698", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305406212, "dur": 52, "ph": "X", "name": "ReadAsync 698", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305406268, "dur": 57, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305406327, "dur": 1, "ph": "X", "name": "ProcessMessages 608", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305406329, "dur": 51, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305406382, "dur": 1, "ph": "X", "name": "ProcessMessages 527", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305406384, "dur": 52, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305406439, "dur": 1, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305406441, "dur": 51, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305406496, "dur": 1, "ph": "X", "name": "ProcessMessages 559", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305406498, "dur": 57, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305406558, "dur": 1, "ph": "X", "name": "ProcessMessages 623", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305406560, "dur": 59, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305406621, "dur": 1, "ph": "X", "name": "ProcessMessages 732", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305406623, "dur": 36, "ph": "X", "name": "ReadAsync 732", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305406661, "dur": 40, "ph": "X", "name": "ReadAsync 91", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305406704, "dur": 43, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305406750, "dur": 1, "ph": "X", "name": "ProcessMessages 674", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305406751, "dur": 41, "ph": "X", "name": "ReadAsync 674", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305406795, "dur": 43, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305406842, "dur": 36, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305406881, "dur": 42, "ph": "X", "name": "ReadAsync 103", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305406925, "dur": 1, "ph": "X", "name": "ProcessMessages 700", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305406929, "dur": 58, "ph": "X", "name": "ReadAsync 700", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305406990, "dur": 2, "ph": "X", "name": "ProcessMessages 630", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305406993, "dur": 51, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305407047, "dur": 1, "ph": "X", "name": "ProcessMessages 618", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305407050, "dur": 54, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305407106, "dur": 1, "ph": "X", "name": "ProcessMessages 369", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305407108, "dur": 57, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305407167, "dur": 1, "ph": "X", "name": "ProcessMessages 833", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305407168, "dur": 45, "ph": "X", "name": "ReadAsync 833", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305407215, "dur": 1, "ph": "X", "name": "ProcessMessages 562", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305407217, "dur": 42, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305407261, "dur": 1, "ph": "X", "name": "ProcessMessages 467", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305407263, "dur": 36, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305407301, "dur": 45, "ph": "X", "name": "ReadAsync 167", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305407349, "dur": 1, "ph": "X", "name": "ProcessMessages 512", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305407350, "dur": 45, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305407399, "dur": 53, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305407454, "dur": 1, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305407456, "dur": 44, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305407502, "dur": 1, "ph": "X", "name": "ProcessMessages 586", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305407503, "dur": 40, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305407546, "dur": 42, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305407591, "dur": 44, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305407638, "dur": 43, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305407683, "dur": 1, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305407684, "dur": 42, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305407728, "dur": 1, "ph": "X", "name": "ProcessMessages 464", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305407730, "dur": 43, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305407777, "dur": 41, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305407819, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305407821, "dur": 42, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305407865, "dur": 1, "ph": "X", "name": "ProcessMessages 405", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305407866, "dur": 42, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305407910, "dur": 1, "ph": "X", "name": "ProcessMessages 371", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305407912, "dur": 42, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305407958, "dur": 41, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305408002, "dur": 40, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305408045, "dur": 46, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305408093, "dur": 1, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305408094, "dur": 43, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305408141, "dur": 41, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305408185, "dur": 41, "ph": "X", "name": "ReadAsync 635", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305408228, "dur": 1, "ph": "X", "name": "ProcessMessages 475", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305408229, "dur": 43, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305408274, "dur": 1, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305408276, "dur": 38, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305408317, "dur": 45, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305408364, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305408365, "dur": 43, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305408411, "dur": 41, "ph": "X", "name": "ReadAsync 627", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305408454, "dur": 1, "ph": "X", "name": "ProcessMessages 371", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305408457, "dur": 44, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305408503, "dur": 1, "ph": "X", "name": "ProcessMessages 708", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305408504, "dur": 36, "ph": "X", "name": "ReadAsync 708", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305408543, "dur": 43, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305408588, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305408590, "dur": 44, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305408636, "dur": 1, "ph": "X", "name": "ProcessMessages 438", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305408638, "dur": 46, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305408685, "dur": 1, "ph": "X", "name": "ProcessMessages 670", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305408687, "dur": 43, "ph": "X", "name": "ReadAsync 670", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305408732, "dur": 1, "ph": "X", "name": "ProcessMessages 497", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305408733, "dur": 41, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305408776, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305408778, "dur": 36, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305408817, "dur": 41, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305408861, "dur": 38, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305408903, "dur": 41, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305408946, "dur": 1, "ph": "X", "name": "ProcessMessages 594", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305408948, "dur": 42, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305408993, "dur": 43, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305409037, "dur": 1, "ph": "X", "name": "ProcessMessages 458", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305409040, "dur": 42, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305409085, "dur": 41, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305409127, "dur": 1, "ph": "X", "name": "ProcessMessages 521", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305409129, "dur": 42, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305409173, "dur": 1, "ph": "X", "name": "ProcessMessages 429", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305409174, "dur": 43, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305409220, "dur": 42, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305409265, "dur": 42, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305409310, "dur": 41, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305409353, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305409355, "dur": 39, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305409396, "dur": 43, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305409441, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305409443, "dur": 41, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305409486, "dur": 1, "ph": "X", "name": "ProcessMessages 382", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305409487, "dur": 43, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305409533, "dur": 1, "ph": "X", "name": "ProcessMessages 573", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305409535, "dur": 41, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305409580, "dur": 42, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305409623, "dur": 1, "ph": "X", "name": "ProcessMessages 427", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305409624, "dur": 35, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305409662, "dur": 24, "ph": "X", "name": "ReadAsync 157", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305409689, "dur": 186, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305409880, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305409883, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305409925, "dur": 298, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305410227, "dur": 50, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305410280, "dur": 6, "ph": "X", "name": "ProcessMessages 736", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305410288, "dur": 38, "ph": "X", "name": "ReadAsync 736", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305410329, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305410331, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305410368, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305410372, "dur": 35, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305410410, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305410413, "dur": 40, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305410456, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305410459, "dur": 34, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305410496, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305410498, "dur": 39, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305410540, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305410542, "dur": 32, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305410577, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305410579, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305410612, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305410614, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305410647, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305410650, "dur": 34, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305410686, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305410689, "dur": 29, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305410722, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305410725, "dur": 30, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305410758, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305410760, "dur": 31, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305410795, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305410798, "dur": 34, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305410835, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305410838, "dur": 34, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305410874, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305410879, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305410914, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305410916, "dur": 32, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305410951, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305410954, "dur": 34, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305410990, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305410993, "dur": 32, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305411029, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305411032, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305411067, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305411070, "dur": 28, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305411100, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305411103, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305411134, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305411165, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305411167, "dur": 29, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305411198, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305411200, "dur": 29, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305411232, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305411234, "dur": 29, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305411266, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305411268, "dur": 30, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305411301, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305411304, "dur": 29, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305411335, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305411338, "dur": 30, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305411371, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305411374, "dur": 79, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305411455, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305411457, "dur": 34, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305411494, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305411497, "dur": 28, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305411527, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305411529, "dur": 25, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305411557, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305411558, "dur": 37, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305411601, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305411633, "dur": 3, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305411637, "dur": 30, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305411669, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305411672, "dur": 30, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305411705, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305411707, "dur": 29, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305411739, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305411742, "dur": 30, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305411774, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305411776, "dur": 29, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305411809, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305411811, "dur": 28, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305411842, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305411845, "dur": 29, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305411878, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305411907, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305411909, "dur": 30, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305411941, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305411943, "dur": 27, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305411974, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305412002, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305412004, "dur": 30, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305412037, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305412040, "dur": 32, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305412074, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305412076, "dur": 28, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305412107, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305412109, "dur": 30, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305412141, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305412143, "dur": 26, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305412172, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305412174, "dur": 128, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305412308, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305412350, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305412353, "dur": 85, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305412442, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305412476, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305412478, "dur": 2335, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305414817, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305414820, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305414862, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305414864, "dur": 34, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305414901, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305414903, "dur": 800, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305415708, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305415710, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305415751, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305415753, "dur": 86, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305415843, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305415845, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305415891, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305415893, "dur": 46, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305415950, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305415992, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305415994, "dur": 5327, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305421336, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305421341, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305421391, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305421394, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305421432, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305421434, "dur": 140, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305421578, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305421581, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305421616, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305421619, "dur": 48, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305421670, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305421673, "dur": 4001, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305425682, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305425685, "dur": 86, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305425773, "dur": 11, "ph": "X", "name": "ProcessMessages 980", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305425786, "dur": 45, "ph": "X", "name": "ReadAsync 980", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305425833, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305425836, "dur": 376, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305426241, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305426244, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305426302, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305426305, "dur": 34, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305426342, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305426344, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305426375, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305426439, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305426443, "dur": 68, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305426562, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305426620, "dur": 956, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305427588, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305427591, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305427697, "dur": 48, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305427749, "dur": 1183, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305428939, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305428943, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305429009, "dur": 4, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305429015, "dur": 317, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305429336, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305429338, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305429375, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305429377, "dur": 49, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305429430, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305429432, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305429477, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305429479, "dur": 73, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305429556, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305429601, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305429603, "dur": 912, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305430520, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305430522, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305430575, "dur": 2, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305430578, "dur": 115, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305430697, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305430700, "dur": 39, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305430743, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305430745, "dur": 54, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305430802, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305430804, "dur": 489, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305431297, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305431299, "dur": 92, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305431395, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305431397, "dur": 47, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305431449, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305431451, "dur": 155, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305431612, "dur": 89, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305431706, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305431709, "dur": 67, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305431781, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305431834, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305431836, "dur": 409, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305432251, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305432293, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305432297, "dur": 58, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305432358, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305432361, "dur": 110, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305432475, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305432478, "dur": 126, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305432607, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305432609, "dur": 141, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305432755, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305432758, "dur": 84, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305432845, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305432849, "dur": 94, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305432948, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305432993, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305432996, "dur": 1416, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305434417, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305434421, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305434469, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305434472, "dur": 54777, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305489255, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305489258, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305489298, "dur": 247, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 22320, "tid": 12884901888, "ts": 1751895305489549, "dur": 5504, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 22320, "tid": 12, "ts": 1751895305516225, "dur": 1091, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 22320, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 22320, "tid": 8589934592, "ts": 1751895305378718, "dur": 107755, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 22320, "tid": 8589934592, "ts": 1751895305486476, "dur": 4, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 22320, "tid": 8589934592, "ts": 1751895305486481, "dur": 1262, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 22320, "tid": 12, "ts": 1751895305517319, "dur": 5, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 22320, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 22320, "tid": 4294967296, "ts": 1751895305339824, "dur": 157183, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 22320, "tid": 4294967296, "ts": 1751895305348326, "dur": 20540, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 22320, "tid": 4294967296, "ts": 1751895305497061, "dur": 4363, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 22320, "tid": 4294967296, "ts": 1751895305501552, "dur": 130, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 22320, "tid": 12, "ts": 1751895305517326, "dur": 14, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1751895305384671, "dur": 2564, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751895305387255, "dur": 439, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751895305387761, "dur": 54, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1751895305387816, "dur": 348, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751895305388708, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_BC34EA19774048D3.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751895305389776, "dur": 6442, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_3BACF753C5B41AC5.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751895305397645, "dur": 2025, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751895305399710, "dur": 89, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751895305400089, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751895305400223, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751895305400701, "dur": 100, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.InputSystem.ref.dll_FD2EC87C14EBE081.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751895305400806, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751895305401017, "dur": 125, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751895305401319, "dur": 98, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Common.ref.dll_EE36537354EA42C8.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751895305401769, "dur": 98, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751895305404446, "dur": 106, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751895305404627, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751895305404816, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751895305405119, "dur": 85, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751895305405210, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751895305405343, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751895305405468, "dur": 80, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751895305405553, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751895305388199, "dur": 21586, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751895305409794, "dur": 79198, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751895305488993, "dur": 189, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751895305489271, "dur": 58, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751895305489347, "dur": 1124, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1751895305388697, "dur": 21141, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751895305409845, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_0E44AF698B21F99B.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751895305409940, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751895305410103, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_4DE8BB45CF6CF9CC.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751895305410393, "dur": 110, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_3520FC5D8B4827F4.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751895305410612, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751895305411086, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751895305411503, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751895305411735, "dur": 3138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751895305414981, "dur": 373, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751895305415795, "dur": 565, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Flow\\Framework\\Math\\Scalar\\ScalarDivide.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751895305415355, "dur": 1119, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751895305416475, "dur": 508, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751895305416984, "dur": 571, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751895305417555, "dur": 1218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751895305418774, "dur": 1341, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751895305420115, "dur": 916, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751895305421032, "dur": 874, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751895305421907, "dur": 746, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751895305422654, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751895305422883, "dur": 695, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751895305423641, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751895305423887, "dur": 4349, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751895305428313, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751895305428509, "dur": 400, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751895305428981, "dur": 465, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751895305429450, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751895305429527, "dur": 1067, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751895305430619, "dur": 60, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751895305430679, "dur": 754, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751895305431433, "dur": 282, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751895305431748, "dur": 619, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751895305432422, "dur": 440, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751895305432863, "dur": 468, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751895305433358, "dur": 55637, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751895305388560, "dur": 21251, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751895305409823, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_2784FA9BFF134207.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751895305410104, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751895305410180, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_86082FB9E11717DE.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751895305410446, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_0D58F8E6C2E92DF4.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751895305410597, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_5BB3CA76865503EA.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751895305410894, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_E43C745D5056CF76.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751895305411047, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_56315C1AB8A5DFB9.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751895305411162, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751895305411281, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751895305411337, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751895305412223, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751895305412448, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751895305412665, "dur": 409, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751895305413075, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751895305413296, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751895305413503, "dur": 358, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751895305413862, "dur": 709, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751895305414571, "dur": 1171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751895305415743, "dur": 1161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751895305416904, "dur": 603, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751895305417507, "dur": 800, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751895305418308, "dur": 743, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751895305419051, "dur": 1287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751895305420338, "dur": 805, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751895305421144, "dur": 1302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751895305422923, "dur": 797, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\Converters\\Unity\\Keyframe_DirectConverter.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751895305422447, "dur": 1689, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751895305424137, "dur": 602, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751895305424788, "dur": 1487, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751895305426427, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751895305426621, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751895305426885, "dur": 112, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751895305427049, "dur": 172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751895305427221, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751895305427696, "dur": 276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751895305427973, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751895305428105, "dur": 425, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751895305428531, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751895305428674, "dur": 814, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751895305429488, "dur": 1126, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751895305430614, "dur": 69, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751895305430684, "dur": 738, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751895305431423, "dur": 312, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751895305431735, "dur": 685, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751895305432420, "dur": 458, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751895305432879, "dur": 56110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751895305388682, "dur": 21140, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751895305409828, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_7359E91D15751A95.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751895305410109, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_676A4B919D1115D9.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751895305410372, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_11834CDAF2387C83.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751895305410611, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_B9448EEF9FF8FB2A.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751895305410848, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_92A1593FB10DA16D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751895305410955, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1751895305411202, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751895305411927, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751895305412251, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751895305412520, "dur": 355, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751895305412876, "dur": 361, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751895305413238, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751895305413458, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751895305413661, "dur": 753, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751895305414414, "dur": 552, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751895305414988, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751895305415228, "dur": 842, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751895305416070, "dur": 568, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751895305416638, "dur": 475, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751895305417114, "dur": 523, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751895305417638, "dur": 519, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751895305418158, "dur": 872, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751895305419159, "dur": 600, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Core\\Inspection\\Primitives\\ByteInspector.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751895305419030, "dur": 1389, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751895305420419, "dur": 757, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751895305421177, "dur": 878, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751895305422056, "dur": 968, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751895305423025, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751895305423205, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751895305423369, "dur": 2040, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751895305425490, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751895305425695, "dur": 696, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751895305426392, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751895305426485, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751895305426699, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751895305427054, "dur": 338, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751895305427392, "dur": 325, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751895305428091, "dur": 201, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751895305428334, "dur": 333, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751895305428668, "dur": 301, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751895305428994, "dur": 450, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751895305429480, "dur": 1128, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751895305430608, "dur": 68, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751895305430707, "dur": 714, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751895305431422, "dur": 314, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751895305431736, "dur": 647, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751895305432419, "dur": 428, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751895305432880, "dur": 56108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751895305388778, "dur": 21138, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751895305410066, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751895305410396, "dur": 125, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_F0DA661B5DCD99FC.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751895305410608, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_1D503C7A8290C7BE.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751895305411221, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751895305411351, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751895305411489, "dur": 137, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751895305411627, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751895305411713, "dur": 433, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751895305412190, "dur": 328, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751895305412551, "dur": 411, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751895305412962, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751895305413176, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751895305413385, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751895305413603, "dur": 753, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751895305414356, "dur": 646, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751895305415002, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751895305415276, "dur": 1123, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751895305416399, "dur": 1249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751895305417648, "dur": 788, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751895305418437, "dur": 689, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751895305419227, "dur": 514, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Core\\Descriptors\\Descriptor.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751895305419127, "dur": 1085, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751895305420212, "dur": 799, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751895305421012, "dur": 647, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751895305421744, "dur": 842, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751895305422627, "dur": 286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751895305422951, "dur": 644, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751895305423662, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751895305423898, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751895305424094, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751895305424295, "dur": 586, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751895305424882, "dur": 418, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751895305425596, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1751895305425769, "dur": 1016, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751895305426786, "dur": 303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751895305427090, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751895305427339, "dur": 666, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751895305428092, "dur": 318, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751895305428410, "dur": 266, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751895305428677, "dur": 817, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751895305429494, "dur": 1103, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751895305430598, "dur": 86, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751895305430684, "dur": 744, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751895305431428, "dur": 301, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751895305431730, "dur": 684, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751895305432414, "dur": 451, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751895305432865, "dur": 55012, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751895305488728, "dur": 232, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "E:/Unity/Editior/6000.0.34f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 4, "ts": 1751895305487878, "dur": 1088, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751895305388825, "dur": 21105, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751895305410029, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_BC34EA19774048D3.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751895305410107, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751895305410384, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_7049A087AD26B808.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751895305410492, "dur": 147, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_D397B83A68A481D1.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751895305410640, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_FB81041CC44652D8.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751895305410962, "dur": 310, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_B1D6EB5640F05713.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751895305411280, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751895305411423, "dur": 4342, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751895305415848, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751895305416057, "dur": 5197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751895305421335, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751895305421523, "dur": 1015, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751895305422622, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751895305422878, "dur": 4725, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751895305427686, "dur": 400, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751895305428108, "dur": 1276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751895305429469, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751895305429655, "dur": 882, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751895305430618, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751895305430792, "dur": 528, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751895305431448, "dur": 286, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751895305431734, "dur": 640, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751895305432432, "dur": 451, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751895305432883, "dur": 56116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751895305388885, "dur": 21059, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751895305410107, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_55EDFAE7741AF365.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751895305410512, "dur": 136, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_EABB3BB0B4DAF932.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751895305410649, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_7FF6DD7231CA2BE3.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751895305410902, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751895305411060, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751895305411138, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751895305411265, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751895305411480, "dur": 134, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1751895305412245, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751895305412460, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751895305412662, "dur": 380, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751895305413043, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751895305413261, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751895305413475, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751895305413710, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751895305413941, "dur": 665, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751895305414606, "dur": 1319, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751895305415972, "dur": 1051, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751895305417023, "dur": 1106, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751895305418129, "dur": 756, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751895305419243, "dur": 511, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Core\\Inspection\\Primitives\\UshortInspector.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751895305418886, "dur": 1330, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751895305420216, "dur": 916, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751895305421132, "dur": 851, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751895305421983, "dur": 758, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751895305422743, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751895305422970, "dur": 824, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751895305423794, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751895305423902, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751895305424087, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751895305424289, "dur": 492, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751895305424824, "dur": 622, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751895305425683, "dur": 1126, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751895305426810, "dur": 291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751895305427101, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751895305427357, "dur": 357, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751895305427716, "dur": 279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751895305428025, "dur": 661, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751895305428755, "dur": 727, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751895305429483, "dur": 1120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751895305430603, "dur": 85, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751895305430689, "dur": 727, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751895305431416, "dur": 323, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751895305431739, "dur": 638, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751895305432425, "dur": 416, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751895305432875, "dur": 56118, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751895305388935, "dur": 21025, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751895305410096, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_F9300E41473E6FF2.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751895305410384, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_656AB5FDB0417D9D.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751895305410615, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_7882E8644625FF23.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751895305411096, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751895305411400, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751895305411599, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751895305411744, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751895305411837, "dur": 200, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751895305412051, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751895305412101, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751895305412198, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751895305412400, "dur": 318, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751895305412719, "dur": 468, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751895305413187, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751895305413394, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751895305413609, "dur": 683, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751895305414292, "dur": 681, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751895305414974, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751895305415208, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751895305415446, "dur": 660, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751895305416319, "dur": 516, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Flow\\Framework\\Events\\GUI\\OnPointerUp.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751895305416107, "dur": 1188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751895305417295, "dur": 755, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751895305418050, "dur": 1195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751895305419245, "dur": 1255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751895305420500, "dur": 685, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751895305421185, "dur": 738, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751895305421924, "dur": 857, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751895305422783, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751895305423021, "dur": 1696, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751895305424781, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751895305425010, "dur": 699, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751895305425710, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751895305425903, "dur": 455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751895305426496, "dur": 315, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751895305426811, "dur": 311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751895305427122, "dur": 279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751895305427402, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751895305427693, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751895305427959, "dur": 625, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751895305428662, "dur": 83, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751895305428772, "dur": 690, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751895305429478, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751895305429660, "dur": 966, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751895305430700, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751895305430864, "dur": 779, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751895305431721, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751895305431879, "dur": 436, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751895305432392, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751895305432531, "dur": 264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751895305432873, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751895305432999, "dur": 287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751895305433341, "dur": 55667, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751895305388995, "dur": 21042, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751895305410069, "dur": 311, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751895305410380, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_78F0F79A0FB195D1.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751895305410606, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_40A52831373DC9B0.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751895305410790, "dur": 140, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_7E2204058CA93257.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751895305410984, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751895305411073, "dur": 258, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_2CD7161C662F4679.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751895305411481, "dur": 152, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1751895305411706, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751895305411980, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751895305412064, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751895305412269, "dur": 607, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751895305412877, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751895305413116, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751895305413322, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751895305413524, "dur": 452, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751895305413976, "dur": 560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751895305415023, "dur": 544, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Flow\\Framework\\Codebase\\LiteralDescriptor.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751895305414536, "dur": 1207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751895305415744, "dur": 743, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751895305416488, "dur": 719, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751895305417207, "dur": 838, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751895305418045, "dur": 651, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751895305418696, "dur": 1301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751895305419998, "dur": 1124, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751895305421122, "dur": 939, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751895305422061, "dur": 721, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751895305422784, "dur": 238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751895305423044, "dur": 1667, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751895305424771, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751895305424971, "dur": 1310, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751895305426282, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751895305426528, "dur": 287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751895305426816, "dur": 343, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751895305427160, "dur": 715, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751895305427923, "dur": 66, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751895305428088, "dur": 327, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751895305428415, "dur": 257, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751895305428672, "dur": 832, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751895305429504, "dur": 1092, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751895305430627, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751895305430701, "dur": 698, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751895305431442, "dur": 254, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751895305431723, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751895305431920, "dur": 420, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751895305432411, "dur": 458, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751895305432869, "dur": 56122, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751895305493806, "dur": 933, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 22320, "tid": 12, "ts": 1751895305517828, "dur": 2348, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 22320, "tid": 12, "ts": 1751895305520210, "dur": 1966, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 22320, "tid": 12, "ts": 1751895305511609, "dur": 11652, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}