Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.34f1 (5ab2d9ed9190) revision 5944025'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.22631) 64bit CoreSingleLanguage' Language: 'en' Physical Memory: 16251 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
E:\Unity\Editior\6000.0.34f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
E:/Unity/Projects/t3essl8r
-logFile
Logs/AssetImportWorker0.log
-srvPort
51198
-job-worker-count
3
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: E:/Unity/Projects/t3essl8r
E:/Unity/Projects/t3essl8r
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [21156]  Target information:

Player connection [21156]  * "[IP] *********** [Port] 0 [Flags] 2 [Guid] 2062943119 [EditorId] 2062943119 [Version] 1048832 [Id] WindowsEditor(7,Atichat) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [21156] Host joined multi-casting on [***********:54997]...
Player connection [21156] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 3
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 2.66 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.34f1 (5ab2d9ed9190)
[Subsystems] Discovering subsystems at path E:/Unity/Editior/6000.0.34f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path E:/Unity/Projects/t3essl8r/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce GTX 1650 Ti (ID=0x1f95)
    Vendor:   NVIDIA
    VRAM:     3935 MB
    Driver:   32.0.15.7680
Initialize mono
Mono path[0] = 'E:/Unity/Editior/6000.0.34f1/Editor/Data/Managed'
Mono path[1] = 'E:/Unity/Editior/6000.0.34f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'E:/Unity/Editior/6000.0.34f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56652
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: E:/Unity/Editior/6000.0.34f1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: E:/Unity/Editior/6000.0.34f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.004583 seconds.
- Loaded All Assemblies, in  0.827 seconds
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.569 seconds
Domain Reload Profiling: 1396ms
	BeginReloadAssembly (334ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (82ms)
	RebuildNativeTypeToScriptingClass (18ms)
	initialDomainReloadingComplete (72ms)
	LoadAllAssembliesAndSetupDomain (320ms)
		LoadAssemblies (335ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (313ms)
			TypeCache.Refresh (312ms)
				TypeCache.ScanAssembly (289ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (570ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (507ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (30ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (81ms)
			ProcessInitializeOnLoadAttributes (288ms)
			ProcessInitializeOnLoadMethodAttributes (103ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.333 seconds
Refreshing native plugins compatible for Editor in 1.99 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.986 seconds
Domain Reload Profiling: 2319ms
	BeginReloadAssembly (370ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (14ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (53ms)
	RebuildCommonClasses (69ms)
	RebuildNativeTypeToScriptingClass (31ms)
	initialDomainReloadingComplete (70ms)
	LoadAllAssembliesAndSetupDomain (791ms)
		LoadAssemblies (604ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (420ms)
			TypeCache.Refresh (333ms)
				TypeCache.ScanAssembly (305ms)
			BuildScriptInfoCaches (71ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (987ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (785ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (145ms)
			ProcessInitializeOnLoadAttributes (572ms)
			ProcessInitializeOnLoadMethodAttributes (52ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.02 seconds
Refreshing native plugins compatible for Editor in 2.31 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 8 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3705 unused Assets / (3.0 MB). Loaded Objects now: 4205.
Memory consumption went from 91.5 MB to 88.5 MB.
Total: 39.286000 ms (FindLiveObjects: 0.976500 ms CreateObjectMapping: 13.262500 ms MarkObjects: 16.185500 ms  DeleteObjects: 8.859400 ms)

========================================================================
Received Import Request.
  Time since last request: 84034.080019 seconds.
  path: Assets/Shaders
  artifactKey: Guid(8b41644422168b347b4be485058dbd05) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Shaders using Guid(8b41644422168b347b4be485058dbd05) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3da53b29580629d38583391b111b7ad4') in 0.0118253 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 20.340688 seconds.
  path: Assets/Shaders/Toon.shader
  artifactKey: Guid(2f457763e2aed6b4bbe0c7712837a9ff) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Shaders/Toon.shader using Guid(2f457763e2aed6b4bbe0c7712837a9ff) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'fe3bfb70feb77468c5154ddd0bcedd85') in 0.0194966 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 14.309732 seconds.
  path: Assets/Material
  artifactKey: Guid(0a44f1a06db08dd4090eaa121e4a1a7c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Material using Guid(0a44f1a06db08dd4090eaa121e4a1a7c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '614fab868f01deb33c4f256d6b162f56') in 0.0005227 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 2.967730 seconds.
  path: Assets/Materials
  artifactKey: Guid(0a44f1a06db08dd4090eaa121e4a1a7c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Materials using Guid(0a44f1a06db08dd4090eaa121e4a1a7c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7393fdd6741a3097138698714f611f89') in 0.0004485 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 5.005952 seconds.
  path: Assets/Materials/New Folder
  artifactKey: Guid(9ad0a68e50fd5974d91c0ec8b4183de8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Materials/New Folder using Guid(9ad0a68e50fd5974d91c0ec8b4183de8) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6cee6c48a5765c52a05b1ba47edf5c1a') in 0.0004877 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 6.660690 seconds.
  path: Assets/Materials/Toon.mat
  artifactKey: Guid(bb29dd654592552448864d4451f56f65) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Materials/Toon.mat using Guid(bb29dd654592552448864d4451f56f65) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7a08f259f5dbfea7667a7ee6416c3ed8') in 0.2443151 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 35.672832 seconds.
  path: Assets/Shaders/Toon_unlit.shader
  artifactKey: Guid(b9caf89720f9f6f4da8f163bf29d45c6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Shaders/Toon_unlit.shader using Guid(b9caf89720f9f6f4da8f163bf29d45c6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0867a7c51909a7aba5e9f32055ae34b2') in 0.0013831 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 23.319953 seconds.
  path: Assets/Materials/Toon unlit.mat
  artifactKey: Guid(c63321f1124ede246aebb52a626b99d9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Materials/Toon unlit.mat using Guid(c63321f1124ede246aebb52a626b99d9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'eb8a448a09127eae73f2acd7718c8878') in 0.0160101 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.973 seconds
Refreshing native plugins compatible for Editor in 2.42 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.977 seconds
Domain Reload Profiling: 1953ms
	BeginReloadAssembly (287ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (59ms)
	RebuildCommonClasses (63ms)
	RebuildNativeTypeToScriptingClass (27ms)
	initialDomainReloadingComplete (41ms)
	LoadAllAssembliesAndSetupDomain (557ms)
		LoadAssemblies (464ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (265ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (233ms)
			ResolveRequiredComponents (19ms)
	FinalizeReload (978ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (762ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (11ms)
			BeforeProcessingInitializeOnLoad (154ms)
			ProcessInitializeOnLoadAttributes (546ms)
			ProcessInitializeOnLoadMethodAttributes (43ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 2.79 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3705 unused Assets / (1.6 MB). Loaded Objects now: 4249.
Memory consumption went from 96.4 MB to 94.8 MB.
Total: 14.306800 ms (FindLiveObjects: 0.636500 ms CreateObjectMapping: 1.643900 ms MarkObjects: 9.285600 ms  DeleteObjects: 2.738600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.961 seconds
Refreshing native plugins compatible for Editor in 2.06 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.154 seconds
Domain Reload Profiling: 2117ms
	BeginReloadAssembly (331ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (61ms)
	RebuildCommonClasses (75ms)
	RebuildNativeTypeToScriptingClass (20ms)
	initialDomainReloadingComplete (29ms)
	LoadAllAssembliesAndSetupDomain (507ms)
		LoadAssemblies (461ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (252ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (231ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (1155ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (900ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (132ms)
			ProcessInitializeOnLoadAttributes (678ms)
			ProcessInitializeOnLoadMethodAttributes (71ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (16ms)
Refreshing native plugins compatible for Editor in 3.09 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3705 unused Assets / (1.7 MB). Loaded Objects now: 4249.
Memory consumption went from 96.3 MB to 94.6 MB.
Total: 16.647000 ms (FindLiveObjects: 1.024800 ms CreateObjectMapping: 2.160000 ms MarkObjects: 10.391700 ms  DeleteObjects: 3.067700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.801 seconds
Refreshing native plugins compatible for Editor in 1.32 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.847 seconds
Domain Reload Profiling: 1651ms
	BeginReloadAssembly (279ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (63ms)
	RebuildCommonClasses (46ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (35ms)
	LoadAllAssembliesAndSetupDomain (426ms)
		LoadAssemblies (386ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (201ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (181ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (848ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (682ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (115ms)
			ProcessInitializeOnLoadAttributes (516ms)
			ProcessInitializeOnLoadMethodAttributes (37ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 4.19 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3705 unused Assets / (1.8 MB). Loaded Objects now: 4249.
Memory consumption went from 96.3 MB to 94.6 MB.
Total: 16.507800 ms (FindLiveObjects: 0.616900 ms CreateObjectMapping: 1.725300 ms MarkObjects: 11.140400 ms  DeleteObjects: 3.023400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 3232.317796 seconds.
  path: Assets/Materials/Toon.mat
  artifactKey: Guid(bb29dd654592552448864d4451f56f65) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Materials/Toon.mat using Guid(bb29dd654592552448864d4451f56f65) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a7029b0150f3c2579731c84993f51a68') in 0.3520454 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

