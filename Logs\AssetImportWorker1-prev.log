Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.34f1 (5ab2d9ed9190) revision 5944025'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.22631) 64bit CoreSingleLanguage' Language: 'en' Physical Memory: 16251 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
E:\Unity\Editior\6000.0.34f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker1
-projectPath
E:/Unity/Projects/t3essl8r
-logFile
Logs/AssetImportWorker1.log
-srvPort
51198
-job-worker-count
3
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: E:/Unity/Projects/t3essl8r
E:/Unity/Projects/t3essl8r
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [22424]  Target information:

Player connection [22424]  * "[IP] *********** [Port] 0 [Flags] 2 [Guid] 3702410119 [EditorId] 3702410119 [Version] 1048832 [Id] WindowsEditor(7,Atichat) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [22424] Host joined multi-casting on [***********:54997]...
Player connection [22424] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 3
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 3.28 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.34f1 (5ab2d9ed9190)
[Subsystems] Discovering subsystems at path E:/Unity/Editior/6000.0.34f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path E:/Unity/Projects/t3essl8r/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce GTX 1650 Ti (ID=0x1f95)
    Vendor:   NVIDIA
    VRAM:     3935 MB
    Driver:   32.0.15.7680
Initialize mono
Mono path[0] = 'E:/Unity/Editior/6000.0.34f1/Editor/Data/Managed'
Mono path[1] = 'E:/Unity/Editior/6000.0.34f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'E:/Unity/Editior/6000.0.34f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56156
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: E:/Unity/Editior/6000.0.34f1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: E:/Unity/Editior/6000.0.34f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.004172 seconds.
- Loaded All Assemblies, in  0.819 seconds
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.567 seconds
Domain Reload Profiling: 1385ms
	BeginReloadAssembly (332ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (70ms)
	RebuildNativeTypeToScriptingClass (22ms)
	initialDomainReloadingComplete (70ms)
	LoadAllAssembliesAndSetupDomain (325ms)
		LoadAssemblies (333ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (316ms)
			TypeCache.Refresh (314ms)
				TypeCache.ScanAssembly (290ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (567ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (504ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (30ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (80ms)
			ProcessInitializeOnLoadAttributes (281ms)
			ProcessInitializeOnLoadMethodAttributes (106ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.343 seconds
Refreshing native plugins compatible for Editor in 1.80 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.978 seconds
Domain Reload Profiling: 2320ms
	BeginReloadAssembly (374ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (56ms)
	RebuildCommonClasses (70ms)
	RebuildNativeTypeToScriptingClass (26ms)
	initialDomainReloadingComplete (73ms)
	LoadAllAssembliesAndSetupDomain (797ms)
		LoadAssemblies (620ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (416ms)
			TypeCache.Refresh (325ms)
				TypeCache.ScanAssembly (295ms)
			BuildScriptInfoCaches (75ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (979ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (776ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (148ms)
			ProcessInitializeOnLoadAttributes (564ms)
			ProcessInitializeOnLoadMethodAttributes (50ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (15ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.02 seconds
Refreshing native plugins compatible for Editor in 2.40 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 8 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3705 unused Assets / (1.5 MB). Loaded Objects now: 4205.
Memory consumption went from 91.4 MB to 89.9 MB.
Total: 31.697300 ms (FindLiveObjects: 0.924000 ms CreateObjectMapping: 1.162900 ms MarkObjects: 26.393300 ms  DeleteObjects: 3.215200 ms)

========================================================================
Received Import Request.
  Time since last request: 84034.061920 seconds.
  path: Assets/New Terrain.asset
  artifactKey: Guid(449d718fd565b4a45ac0006e8d7b109e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/New Terrain.asset using Guid(449d718fd565b4a45ac0006e8d7b109e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4f9382a73074a06d270a04f3ba36c9d4') in 0.0718352 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.951 seconds
Refreshing native plugins compatible for Editor in 3.37 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.987 seconds
Domain Reload Profiling: 1941ms
	BeginReloadAssembly (281ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (55ms)
	RebuildCommonClasses (53ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (41ms)
	LoadAllAssembliesAndSetupDomain (555ms)
		LoadAssemblies (468ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (260ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (234ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (988ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (777ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (161ms)
			ProcessInitializeOnLoadAttributes (556ms)
			ProcessInitializeOnLoadMethodAttributes (45ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 2.73 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3705 unused Assets / (1.5 MB). Loaded Objects now: 4209.
Memory consumption went from 91.9 MB to 90.5 MB.
Total: 13.921300 ms (FindLiveObjects: 0.736500 ms CreateObjectMapping: 1.343100 ms MarkObjects: 9.154900 ms  DeleteObjects: 2.684600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.928 seconds
Refreshing native plugins compatible for Editor in 1.89 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.118 seconds
Domain Reload Profiling: 2049ms
	BeginReloadAssembly (328ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (66ms)
	RebuildCommonClasses (60ms)
	RebuildNativeTypeToScriptingClass (23ms)
	initialDomainReloadingComplete (30ms)
	LoadAllAssembliesAndSetupDomain (489ms)
		LoadAssemblies (445ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (237ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (216ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (1119ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (889ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (10ms)
			SetLoadedEditorAssemblies (10ms)
			BeforeProcessingInitializeOnLoad (144ms)
			ProcessInitializeOnLoadAttributes (667ms)
			ProcessInitializeOnLoadMethodAttributes (54ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (15ms)
Refreshing native plugins compatible for Editor in 3.27 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3705 unused Assets / (1.8 MB). Loaded Objects now: 4209.
Memory consumption went from 91.9 MB to 90.0 MB.
Total: 14.357000 ms (FindLiveObjects: 0.673500 ms CreateObjectMapping: 0.885500 ms MarkObjects: 8.803100 ms  DeleteObjects: 3.991900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.789 seconds
Refreshing native plugins compatible for Editor in 1.50 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.854 seconds
Domain Reload Profiling: 1644ms
	BeginReloadAssembly (273ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (57ms)
	RebuildCommonClasses (43ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (32ms)
	LoadAllAssembliesAndSetupDomain (425ms)
		LoadAssemblies (384ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (198ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (182ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (855ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (680ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (109ms)
			ProcessInitializeOnLoadAttributes (518ms)
			ProcessInitializeOnLoadMethodAttributes (41ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 1.95 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 2 Unused Serialized files (Serialized files now loaded: 0)
Unloading 3705 unused Assets / (1.8 MB). Loaded Objects now: 4209.
Memory consumption went from 91.9 MB to 90.1 MB.
Total: 13.788500 ms (FindLiveObjects: 0.595100 ms CreateObjectMapping: 1.079200 ms MarkObjects: 8.334300 ms  DeleteObjects: 3.778500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 3340.845395 seconds.
  path: Assets/Materials/Toon unlit.mat
  artifactKey: Guid(c63321f1124ede246aebb52a626b99d9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Materials/Toon unlit.mat using Guid(c63321f1124ede246aebb52a626b99d9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'dce3bd5fa58a9a91bc50bd8a3cd680f4') in 0.2787997 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

